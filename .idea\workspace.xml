<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="a2f0fdcf-d5ff-45f1-bcde-e2d0ab85b050" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ExportToHTMLSettings">
    <option name="OUTPUT_DIRECTORY" value="$PROJECT_DIR$/../pythonProject\exportToHTML" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Jupyter Notebook" />
        <option value="Python Script" />
      </list>
    </option>
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="ProjectId" id="2Pxz9vjw8AxUtD6DL75p7spila8" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;WebServerToolWindowFactoryState&quot;: &quot;false&quot;,
    &quot;last_opened_file_path&quot;: &quot;E:/pythonProject&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="E:\pythonProject" />
      <recent name="E:\pythonProject\upsampling_data_model" />
    </key>
  </component>
  <component name="RunManager" selected="Python.LDM_0721_V2">
    <configuration name="LDM_0716_V4" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="pythonProject" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/Novelty_detection/code" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/Novelty_detection/code/LDM_0716_V4.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="LDM_0716_V6" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="pythonProject" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/Novelty_detection/code" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/Novelty_detection/code/LDM_0716_V6.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="LDM_0716_V8" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="pythonProject" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/Novelty_detection/code" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/Novelty_detection/code/LDM_0716_V8.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="LDM_0721" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="pythonProject" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/Novelty_detection/code" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/Novelty_detection/code/LDM_0721.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="LDM_0721_V2" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="pythonProject" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/Novelty_detection/code" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/Novelty_detection/code/LDM_0721_V2.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="main" type="PythonConfigurationType" factoryName="Python" nameIsGenerated="true">
      <module name="pythonProject" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/main.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Python.LDM_0721_V2" />
        <item itemvalue="Python.LDM_0721" />
        <item itemvalue="Python.LDM_0716_V6" />
        <item itemvalue="Python.LDM_0716_V8" />
        <item itemvalue="Python.LDM_0716_V4" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="a2f0fdcf-d5ff-45f1-bcde-e2d0ab85b050" name="Changes" comment="" />
      <created>1684413372658</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1684413372658</updated>
      <workItem from="1684413373826" duration="708000" />
      <workItem from="1684646740755" duration="623000" />
      <workItem from="1684647581847" duration="1344000" />
      <workItem from="1684648944434" duration="18980000" />
      <workItem from="1684761687771" duration="5554000" />
      <workItem from="1684806008750" duration="25290000" />
      <workItem from="1684892452907" duration="11622000" />
      <workItem from="1684979322075" duration="25781000" />
      <workItem from="1685064843738" duration="1698000" />
      <workItem from="1685180101170" duration="4898000" />
      <workItem from="1685332377685" duration="3930000" />
      <workItem from="1685411154627" duration="26232000" />
      <workItem from="1685499545051" duration="12963000" />
      <workItem from="1685583844661" duration="23653000" />
      <workItem from="1685669717504" duration="18070000" />
      <workItem from="1685929209987" duration="18379000" />
      <workItem from="1685958304097" duration="3191000" />
      <workItem from="1686015529691" duration="24642000" />
      <workItem from="1686062593575" duration="1243000" />
      <workItem from="1686100055830" duration="553000" />
      <workItem from="1686100619994" duration="264000" />
      <workItem from="1686100896796" duration="43612000" />
      <workItem from="1686275934139" duration="3280000" />
      <workItem from="1686365747424" duration="13677000" />
      <workItem from="1686535613115" duration="23825000" />
      <workItem from="1686620756834" duration="10668000" />
      <workItem from="1686707078626" duration="2982000" />
      <workItem from="1686793275119" duration="8436000" />
      <workItem from="1686887199653" duration="990000" />
      <workItem from="1688353773809" duration="1916000" />
      <workItem from="1688355821187" duration="6266000" />
      <workItem from="1690622033179" duration="7210000" />
      <workItem from="1690687346021" duration="6354000" />
      <workItem from="1690772647652" duration="21904000" />
      <workItem from="1690854076994" duration="25430000" />
      <workItem from="1690940821423" duration="16158000" />
      <workItem from="1691028130394" duration="14283000" />
      <workItem from="1693211700438" duration="3979000" />
      <workItem from="1693302767787" duration="2531000" />
      <workItem from="1693316643566" duration="3171000" />
      <workItem from="1693361851643" duration="4952000" />
      <workItem from="1694925004865" duration="1074000" />
      <workItem from="1694953182208" duration="6307000" />
      <workItem from="1695001579250" duration="21998000" />
      <workItem from="1695347435465" duration="9294000" />
      <workItem from="1695872431328" duration="10000000" />
      <workItem from="1696566587959" duration="12682000" />
      <workItem from="1696645555449" duration="8466000" />
      <workItem from="1701747684462" duration="1382000" />
      <workItem from="1703082248466" duration="629000" />
      <workItem from="1709625698923" duration="1089000" />
      <workItem from="1712563575054" duration="602000" />
      <workItem from="1712734528683" duration="5258000" />
      <workItem from="1712819463737" duration="8861000" />
      <workItem from="1712885502329" duration="2888000" />
      <workItem from="1713144835778" duration="1594000" />
      <workItem from="1713490249621" duration="52520000" />
      <workItem from="1714115947560" duration="603000" />
      <workItem from="1714361281651" duration="10467000" />
      <workItem from="1714462217830" duration="7789000" />
      <workItem from="1714701846122" duration="34646000" />
      <workItem from="1715044880070" duration="22505000" />
      <workItem from="1715131259777" duration="19525000" />
      <workItem from="1715220344186" duration="24915000" />
      <workItem from="1715649463300" duration="49247000" />
      <workItem from="1715822110440" duration="27958000" />
      <workItem from="1715909908871" duration="24308000" />
      <workItem from="1715997885042" duration="29373000" />
      <workItem from="1716296359683" duration="4024000" />
      <workItem from="1716340698039" duration="18809000" />
      <workItem from="1716426810502" duration="1196000" />
      <workItem from="1716511031643" duration="10000" />
      <workItem from="1716772925120" duration="599000" />
      <workItem from="1716951943103" duration="28548000" />
      <workItem from="1717146245367" duration="498000" />
      <workItem from="1717378317036" duration="26806000" />
      <workItem from="1717464595483" duration="25327000" />
      <workItem from="1717550004139" duration="11755000" />
      <workItem from="1717637178552" duration="24337000" />
      <workItem from="1717723297892" duration="1120000" />
      <workItem from="1717725314594" duration="285000" />
      <workItem from="1717725608353" duration="13200000" />
      <workItem from="1717755420714" duration="8508000" />
      <workItem from="1718155271760" duration="8236000" />
      <workItem from="1718241575766" duration="17994000" />
      <workItem from="1718326990123" duration="7181000" />
      <workItem from="1719185064253" duration="46590000" />
      <workItem from="1719796670435" duration="14217000" />
      <workItem from="1719882710632" duration="3908000" />
      <workItem from="1719976191829" duration="8196000" />
      <workItem from="1720055961895" duration="5078000" />
      <workItem from="1720161562796" duration="3051000" />
      <workItem from="1721889635138" duration="3458000" />
      <workItem from="1721957155063" duration="10453000" />
      <workItem from="1721984864249" duration="10000" />
      <workItem from="1722159065448" duration="7744000" />
      <workItem from="1722215789952" duration="9070000" />
      <workItem from="1722235440118" duration="1927000" />
      <workItem from="1722254038476" duration="3360000" />
      <workItem from="1722304412050" duration="3263000" />
      <workItem from="1722321226225" duration="9197000" />
      <workItem from="1722388966107" duration="17705000" />
      <workItem from="1722475951398" duration="18703000" />
      <workItem from="1722561678640" duration="9503000" />
      <workItem from="1722820432908" duration="15466000" />
      <workItem from="1722906813499" duration="14839000" />
      <workItem from="1722997303272" duration="13732000" />
      <workItem from="1723081015821" duration="6944000" />
      <workItem from="1723187497224" duration="4965000" />
      <workItem from="1723513406291" duration="9381000" />
      <workItem from="1723710325191" duration="7557000" />
      <workItem from="1723796294545" duration="136000" />
      <workItem from="1724033382621" duration="8638000" />
      <workItem from="1724574829137" duration="6108000" />
      <workItem from="1724640730074" duration="16836000" />
      <workItem from="1724721864765" duration="6278000" />
      <workItem from="1724833499229" duration="1407000" />
      <workItem from="1724899085022" duration="3375000" />
      <workItem from="1725266253013" duration="2922000" />
      <workItem from="1725505817972" duration="553000" />
      <workItem from="1725510013306" duration="260000" />
      <workItem from="1726127948846" duration="2458000" />
      <workItem from="1726192798651" duration="752000" />
      <workItem from="1726622867638" duration="625000" />
      <workItem from="1726729103531" duration="1925000" />
      <workItem from="1726825258145" duration="41000" />
      <workItem from="1726976040700" duration="3063000" />
      <workItem from="1726993552892" duration="3947000" />
      <workItem from="1727147350088" duration="1776000" />
      <workItem from="1727158816562" duration="11272000" />
      <workItem from="1727313585563" duration="6868000" />
      <workItem from="1727358448448" duration="822000" />
      <workItem from="1727593211729" duration="3943000" />
      <workItem from="1727663303370" duration="3116000" />
      <workItem from="1727681032696" duration="9329000" />
      <workItem from="1727770589413" duration="2600000" />
      <workItem from="1727834749580" duration="6009000" />
      <workItem from="1728203556784" duration="2765000" />
      <workItem from="1728356054515" duration="5287000" />
      <workItem from="1728392448380" duration="2562000" />
      <workItem from="1728440749048" duration="6377000" />
      <workItem from="1728475472563" duration="130000" />
      <workItem from="1728524845964" duration="12833000" />
      <workItem from="1728553021426" duration="1972000" />
      <workItem from="1728609333537" duration="6164000" />
      <workItem from="1728629100485" duration="990000" />
      <workItem from="1729129837166" duration="4237000" />
      <workItem from="1729234435902" duration="7950000" />
      <workItem from="1729251811575" duration="2938000" />
      <workItem from="1729413930937" duration="705000" />
      <workItem from="1729473989380" duration="638000" />
      <workItem from="1729578969280" duration="5898000" />
      <workItem from="1733218647334" duration="186000" />
      <workItem from="1736994097816" duration="6340000" />
      <workItem from="1737085803845" duration="1325000" />
      <workItem from="1739521000998" duration="597000" />
      <workItem from="1739522661181" duration="34000" />
      <workItem from="1739714182878" duration="1449000" />
      <workItem from="1739761188473" duration="21493000" />
      <workItem from="1740034574869" duration="7054000" />
      <workItem from="1740100842034" duration="14687000" />
      <workItem from="1740215260804" duration="36303000" />
      <workItem from="1740447042834" duration="24051000" />
      <workItem from="1740532685369" duration="8009000" />
      <workItem from="1740621104978" duration="20578000" />
      <workItem from="1740705826981" duration="9262000" />
      <workItem from="1740725724153" duration="8479000" />
      <workItem from="1740885765842" duration="12000" />
      <workItem from="1740968709614" duration="6418000" />
      <workItem from="1741229952817" duration="1499000" />
      <workItem from="1741247534192" duration="781000" />
      <workItem from="1741263250359" duration="617000" />
      <workItem from="1741267687705" duration="2766000" />
      <workItem from="1741312539142" duration="10429000" />
      <workItem from="1742354481936" duration="20000" />
      <workItem from="1742883621877" duration="7854000" />
      <workItem from="1742914297999" duration="2127000" />
      <workItem from="1742952297169" duration="8123000" />
      <workItem from="1742971785907" duration="11980000" />
      <workItem from="1743038421551" duration="3091000" />
      <workItem from="1743057535368" duration="13808000" />
      <workItem from="1743125654272" duration="8184000" />
      <workItem from="1743143254679" duration="4203000" />
      <workItem from="1743147501486" duration="2600000" />
      <workItem from="1743150139932" duration="2014000" />
      <workItem from="1743154277802" duration="125000" />
      <workItem from="1743304108980" duration="19136000" />
      <workItem from="1743384106947" duration="3701000" />
      <workItem from="1743388020151" duration="709000" />
      <workItem from="1743389259215" duration="1888000" />
      <workItem from="1743391330263" duration="1093000" />
      <workItem from="1743402971964" duration="13513000" />
      <workItem from="1743424458781" duration="5914000" />
      <workItem from="1743470017427" duration="8108000" />
      <workItem from="1743496143730" duration="7083000" />
      <workItem from="1743503369179" duration="785000" />
      <workItem from="1743504171045" duration="14000" />
      <workItem from="1743504189760" duration="57000" />
      <workItem from="1743511494723" duration="5342000" />
      <workItem from="1743558616834" duration="2316000" />
      <workItem from="1743561584375" duration="569000" />
      <workItem from="1743562167349" duration="3340000" />
      <workItem from="1743575667144" duration="8007000" />
      <workItem from="1743584147969" duration="513000" />
      <workItem from="1743650823645" duration="1494000" />
      <workItem from="1743730847070" duration="3236000" />
      <workItem from="1743739313700" duration="1484000" />
      <workItem from="1743751979624" duration="2842000" />
      <workItem from="1743990728015" duration="6302000" />
      <workItem from="1744010271245" duration="5181000" />
      <workItem from="1744078664694" duration="4956000" />
      <workItem from="1744097149896" duration="5910000" />
      <workItem from="1744114656188" duration="1375000" />
      <workItem from="1744161744299" duration="10543000" />
      <workItem from="1744180117597" duration="3065000" />
      <workItem from="1744187441111" duration="811000" />
      <workItem from="1744249103714" duration="3592000" />
      <workItem from="1744268119388" duration="10775000" />
      <workItem from="1744282012602" duration="6802000" />
      <workItem from="1744334792632" duration="7162000" />
      <workItem from="1744352854171" duration="5803000" />
      <workItem from="1744605963225" duration="606000" />
      <workItem from="1744612558713" duration="4176000" />
      <workItem from="1744687078589" duration="20976000" />
      <workItem from="1744767116232" duration="9104000" />
      <workItem from="1744791002497" duration="1213000" />
      <workItem from="1744852857066" duration="761000" />
      <workItem from="1744871258054" duration="19443000" />
      <workItem from="1744902732640" duration="1881000" />
      <workItem from="1744938768345" duration="9051000" />
      <workItem from="1744957662197" duration="9649000" />
      <workItem from="1745032861159" duration="1271000" />
      <workItem from="1745034221689" duration="2878000" />
      <workItem from="1745045398268" duration="3839000" />
      <workItem from="1745121758346" duration="3287000" />
      <workItem from="1745137638085" duration="1532000" />
      <workItem from="1745158173506" duration="815000" />
      <workItem from="1745198266287" duration="157000" />
      <workItem from="1745208526625" duration="827000" />
      <workItem from="1745289728734" duration="4837000" />
      <workItem from="1745302920732" duration="16401000" />
      <workItem from="1745371208965" duration="5812000" />
      <workItem from="1745393384772" duration="5150000" />
      <workItem from="1745457512778" duration="8083000" />
      <workItem from="1745475220244" duration="14202000" />
      <workItem from="1745545219289" duration="7023000" />
      <workItem from="1745825781848" duration="2292000" />
      <workItem from="1745911780926" duration="888000" />
      <workItem from="1745929692590" duration="2226000" />
      <workItem from="1746581870363" duration="1545000" />
      <workItem from="1747273598454" duration="5446000" />
      <workItem from="1747279106942" duration="2387000" />
      <workItem from="1747281989456" duration="652000" />
      <workItem from="1747301694680" duration="1035000" />
      <workItem from="1747314125219" duration="1340000" />
      <workItem from="1747377690174" duration="7085000" />
      <workItem from="1747388831509" duration="1704000" />
      <workItem from="1747536297036" duration="2019000" />
      <workItem from="1747539960298" duration="1345000" />
      <workItem from="1747554459650" duration="6255000" />
      <workItem from="1747621081174" duration="4662000" />
      <workItem from="1747704402125" duration="1364000" />
      <workItem from="1747728282365" duration="22608000" />
      <workItem from="1747879702109" duration="13795000" />
      <workItem from="1747985553802" duration="1080000" />
      <workItem from="1748221612462" duration="84000" />
      <workItem from="1748225332940" duration="4250000" />
      <workItem from="1748239888528" duration="11461000" />
      <workItem from="1748309534590" duration="5036000" />
      <workItem from="1748484194515" duration="1298000" />
      <workItem from="1748487331705" duration="1881000" />
      <workItem from="1748502052720" duration="5843000" />
      <workItem from="1748510290509" duration="4758000" />
      <workItem from="1748523934079" duration="1770000" />
      <workItem from="1748838383712" duration="932000" />
      <workItem from="1748849583058" duration="4788000" />
      <workItem from="1748854863234" duration="611000" />
      <workItem from="1748916865395" duration="15980000" />
      <workItem from="1749002151021" duration="38634000" />
      <workItem from="1749199862104" duration="6809000" />
      <workItem from="1749351786881" duration="17864000" />
      <workItem from="1749440693735" duration="14894000" />
      <workItem from="1749611249090" duration="147000" />
      <workItem from="1749611404533" duration="52000" />
      <workItem from="1749611468185" duration="37000" />
      <workItem from="1749611515658" duration="1137000" />
      <workItem from="1749635189392" duration="725000" />
      <workItem from="1749779615609" duration="12094000" />
      <workItem from="1750057028538" duration="1712000" />
      <workItem from="1750385610834" duration="1914000" />
      <workItem from="1750671920788" duration="346000" />
      <workItem from="1751961265458" duration="1837000" />
      <workItem from="1752295462459" duration="610000" />
      <workItem from="1752717328183" duration="20573000" />
      <workItem from="1752822051364" duration="7658000" />
      <workItem from="1752832778123" duration="13712000" />
      <workItem from="1753081038556" duration="2219000" />
      <workItem from="1753083709055" duration="6938000" />
      <workItem from="1753155984569" duration="4482000" />
      <workItem from="1753167639776" duration="5187000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/main.py</url>
          <line>8</line>
          <option name="timeStamp" value="1" />
        </line-breakpoint>
      </breakpoints>
      <default-breakpoints>
        <breakpoint type="python-exception">
          <properties notifyOnTerminate="true" exception="BaseException">
            <option name="notifyOnTerminate" value="true" />
          </properties>
        </breakpoint>
      </default-breakpoints>
    </breakpoint-manager>
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/MSCNN0731_py$MSCNN0802_4.coverage" NAME="MSCNN0802_4 Coverage Results" MODIFIED="1690964642200" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/interpolation0506_py$interpolation0507.coverage" NAME="interpolation0507 Coverage Results" MODIFIED="1715138032543" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/tradition0730_py$Encoder1010_2.coverage" NAME="Encoder1010_2 Coverage Results" MODIFIED="1728545460374" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0404_V2_py$MSCNN0525.coverage" NAME="MSCNN0525 Coverage Results" MODIFIED="1744336138718" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0519_py$MSCNN0607_4.coverage" NAME="MSCNN0607_4 Coverage Results" MODIFIED="1686122446483" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0731_py$MSCNN0829.coverage" NAME="MSCNN0829 Coverage Results" MODIFIED="1693312342405" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0519_py$MSCNN0608_2.coverage" NAME="MSCNN0608_2 Coverage Results" MODIFIED="1688373531344" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0516_py$LDM_0519.coverage" NAME="LDM_0519 Coverage Results" MODIFIED="1747704417269" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Novelty_detection/code" />
    <SUITE FILE_PATH="coverage/LDM_0418_py$LDM_0418__1_.coverage" NAME="LDM_0418 (1) Coverage Results" MODIFIED="1745208713966" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Novelty_detection/code" />
    <SUITE FILE_PATH="coverage/LDM_0609_py$LDM_0602.coverage" NAME="LDM_0602 Coverage Results" MODIFIED="1749780635134" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Novelty_detection/code/backup" />
    <SUITE FILE_PATH="coverage/tradition0730_py$cnn_transformer1010.coverage" NAME="cnn_transformer1010 Coverage Results" MODIFIED="1728552899328" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0519_py$MSCNN0524.coverage" NAME="MSCNN0524 Coverage Results" MODIFIED="1684916932984" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/swin_transformer0730_1_py$swin_transformer0730_1.coverage" NAME="swin_transformer0730_1 Coverage Results" MODIFIED="1728204967820" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0731_py$MSCNN0731_8.coverage" NAME="MSCNN0731_8 Coverage Results" MODIFIED="1690863170267" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0609_py$LDM_0716_V5.coverage" NAME="LDM_0716_V5 Coverage Results" MODIFIED="1752737966342" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Novelty_detection/code" />
    <SUITE FILE_PATH="coverage/MSCNN0519_py$MSCNN0522_2.coverage" NAME="MSCNN0522_2 Coverage Results" MODIFIED="1684814148548" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0404_V2_py$LDM_VAE_0415.coverage" NAME="LDM_VAE_0415 Coverage Results" MODIFIED="1744877732848" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0519_py$MSCNN0612_2.coverage" NAME="MSCNN0612_2 Coverage Results" MODIFIED="1686572576981" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/swin_transformer0703_py$swin_transformer0728_3.coverage" NAME="swin_transformer0728_3 Coverage Results" MODIFIED="1722166243411" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0220_py$LDM_0602.coverage" NAME="LDM_0602 Coverage Results" MODIFIED="1748853612035" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Novelty_detection/code" />
    <SUITE FILE_PATH="coverage/MSCNN0519_py$MSCNN0602_2.coverage" NAME="MSCNN0602_2 Coverage Results" MODIFIED="1685678632426" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0608_V3_py$LDM_0608_V3.coverage" NAME="LDM_0608_V3 Coverage Results" MODIFIED="1752832878763" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Novelty_detection/code" />
    <SUITE FILE_PATH="coverage/MSCNN0731_py$MSCNN0801_1.coverage" NAME="MSCNN0801_1 Coverage Results" MODIFIED="1690856571030" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0519_py$MSCNN0530_1.coverage" NAME="MSCNN0530_1 Coverage Results" MODIFIED="1685439259760" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/VAE_0218_py$VAE_0218.coverage" NAME="VAE_0218 Coverage Results" MODIFIED="1747275854417" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Novelty_detection/code" />
    <SUITE FILE_PATH="coverage/MSCNN0519_py$MSCNN0523_2.coverage" NAME="MSCNN0523_2 Coverage Results" MODIFIED="1684851994190" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0731_py$MSCNN0731_1.coverage" NAME="MSCNN0731_1 Coverage Results" MODIFIED="1690791594374" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0519_py$MSCNN0612.coverage" NAME="MSCNN0612 Coverage Results" MODIFIED="1686562288600" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0731_py$MSCNN0801_6.coverage" NAME="MSCNN0801_6 Coverage Results" MODIFIED="1690877977052" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0731_py$sub_pixel_convolution0424.coverage" NAME="sub_pixel convolution0424 Coverage Results" MODIFIED="1714702471586" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/interpolation0506_py$swin_transformer0705.coverage" NAME="swin_transformer0705 Coverage Results" MODIFIED="1720162238781" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/upsampling_data_model" />
    <SUITE FILE_PATH="coverage/Kriging_0423_py$Kriging_0424.coverage" NAME="Kriging_0424 Coverage Results" MODIFIED="1745546292803" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0404_V2_py$LDM_0418_V1.coverage" NAME="LDM_0418_V1 Coverage Results" MODIFIED="1744967990973" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0521_py$LDM_0521.coverage" NAME="LDM_0521 Coverage Results" MODIFIED="1748221639031" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Novelty_detection/code" />
    <SUITE FILE_PATH="coverage/heatmap_py$heatmap.coverage" NAME="heatmap Coverage Results" MODIFIED="1729234443919" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0418_py$swin_transformer_0422_V7.coverage" NAME="swin_transformer_0422_V7 Coverage Results" MODIFIED="1745372891273" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0608_V3_py$LDM_0716_V8.coverage" NAME="LDM_0716_V8 Coverage Results" MODIFIED="1753071317915" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Novelty_detection/code" />
    <SUITE FILE_PATH="coverage/MSCNN0801_py$MSCNN0801.coverage" NAME="MSCNN0801 Coverage Results" MODIFIED="1749611794571" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0418_py$LDM_0407__1_.coverage" NAME="LDM_0407 (1) Coverage Results" MODIFIED="1745208580918" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Novelty_detection/code" />
    <SUITE FILE_PATH="coverage/VAE_edge_Puchuang_py$swin_transformer_Puchuang_para.coverage" NAME="swin_transformer_Puchuang_para Coverage Results" MODIFIED="1747282203110" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0731_py$MSCNN0731_6.coverage" NAME="MSCNN0731_6 Coverage Results" MODIFIED="1690806334947" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/swin_transformer_sin0805_py$swin_transformer_sin0805.coverage" NAME="swin_transformer_sin0805 Coverage Results" MODIFIED="1727599379449" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/swin_transformer0730_1_py$tradition0730.coverage" NAME="tradition0730 Coverage Results" MODIFIED="1728205709281" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/swin_transformer0703_py$swin_transformer_sin0805_3.coverage" NAME="swin_transformer_sin0805_3 Coverage Results" MODIFIED="1722863726541" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/VAE_OCSVM_0203_py$VAE_OCSVM_0217.coverage" NAME="VAE_OCSVM_0217 Coverage Results" MODIFIED="1739861829913" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0529_V3_py$LDM_0529_V4.coverage" NAME="LDM_0529_V4 Coverage Results" MODIFIED="1748522884730" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Novelty_detection/code" />
    <SUITE FILE_PATH="coverage/swin_transformer0703_py$sin_cos_noise.coverage" NAME="sin_cos_noise Coverage Results" MODIFIED="1725510226210" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/swin_transformer0703_py$swin_transformer_sin0802.coverage" NAME="swin_transformer_sin0802 Coverage Results" MODIFIED="1722570836733" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0609_py$trm.coverage" NAME="trm Coverage Results" MODIFIED="1750388956844" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0519_py$MSCNN0607.coverage" NAME="MSCNN0607 Coverage Results" MODIFIED="1686102145678" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/interpolation0506_py$transformer0517.coverage" NAME="transformer0517 Coverage Results" MODIFIED="1715918606750" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/swin_transformer0703_py$swin_transformer_sin0805.coverage" NAME="swin_transformer_sin0805 Coverage Results" MODIFIED="1723556201662" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0404_V2_py$LDM_0417.coverage" NAME="LDM_0417 Coverage Results" MODIFIED="1744883450818" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0602_py$LDM_0608_V3.coverage" NAME="LDM_0608_V3 Coverage Results" MODIFIED="1749374162345" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Novelty_detection/code" />
    <SUITE FILE_PATH="coverage/LDM_0609_py$LDM_0717.coverage" NAME="LDM_0717 Coverage Results" MODIFIED="1752719989044" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Novelty_detection/code" />
    <SUITE FILE_PATH="coverage/MSCNN0519_py$MSCNN0607_6.coverage" NAME="MSCNN0607_6 Coverage Results" MODIFIED="1686124879661" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0516_py$LDM_0517.coverage" NAME="LDM_0517 Coverage Results" MODIFIED="1747735860413" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Novelty_detection/code" />
    <SUITE FILE_PATH="coverage/MSCNN0519_py$MSCNN0601_2.coverage" NAME="MSCNN0601_2 Coverage Results" MODIFIED="1685604424346" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0731_py$MSCNN0922.coverage" NAME="MSCNN0922 Coverage Results" MODIFIED="1695369877297" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0731_py$MSCNN0917_1.coverage" NAME="MSCNN0917_1 Coverage Results" MODIFIED="1694961158934" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0519_py$MSCNN0530_3.coverage" NAME="MSCNN0530_3 Coverage Results" MODIFIED="1685454616395" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0602_py$LDM_0604_V3.coverage" NAME="LDM_0604_V3 Coverage Results" MODIFIED="1749105106936" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Novelty_detection/code" />
    <SUITE FILE_PATH="coverage/VAE_OCSVM_0203_py$LDM_0226.coverage" NAME="LDM_0226 Coverage Results" MODIFIED="1740627557478" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0519_py$MSCNN0605_1.coverage" NAME="MSCNN0605_1 Coverage Results" MODIFIED="1685949616765" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/swin_transformer0703_py$swin_transformer0806.coverage" NAME="swin_transformer0806 Coverage Results" MODIFIED="1723021194410" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0731_py$MSCNN0801_8.coverage" NAME="MSCNN0801_8 Coverage Results" MODIFIED="1690965428134" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0519_py$MSCNN0602.coverage" NAME="MSCNN0602 Coverage Results" MODIFIED="1685675204282" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0404_V2_py$LDM_0409.coverage" NAME="LDM_0409 Coverage Results" MODIFIED="1744940887006" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/heatmap_1018_py$LDM_0401_V2.coverage" NAME="LDM_0401_V2 Coverage Results" MODIFIED="1743583737613" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0521_py$LDM_0529.coverage" NAME="LDM_0529 Coverage Results" MODIFIED="1748487369018" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Novelty_detection/code" />
    <SUITE FILE_PATH="coverage/LDM_0602_py$LDM_0605_V3.coverage" NAME="LDM_0605_V3 Coverage Results" MODIFIED="1749202349855" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Novelty_detection/code" />
    <SUITE FILE_PATH="coverage/LDM_0609_py$LDM_0716_V8.coverage" NAME="LDM_0716_V8 Coverage Results" MODIFIED="1752830822559" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Novelty_detection/code" />
    <SUITE FILE_PATH="coverage/LDM_0404_V2_py$LDM_0410.coverage" NAME="LDM_0410 Coverage Results" MODIFIED="1744294058452" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0519_py$CNN0529.coverage" NAME="CNN0529 Coverage Results" MODIFIED="1685436546111" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0609_py$LDM_0716_V3.coverage" NAME="LDM_0716_V3 Coverage Results" MODIFIED="1752734924077" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Novelty_detection/code" />
    <SUITE FILE_PATH="coverage/LDM_0404_V2_py$LDM_0417_V3.coverage" NAME="LDM_0417_V3 Coverage Results" MODIFIED="1744944861396" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0609_py$LDM_0609.coverage" NAME="LDM_0609 Coverage Results" MODIFIED="1752717364271" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Novelty_detection/code" />
    <SUITE FILE_PATH="coverage/LDM_0418_py$tradition0730.coverage" NAME="tradition0730 Coverage Results" MODIFIED="1745379434706" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0418_py$swin_transformer_0422_V5.coverage" NAME="swin_transformer_0422_V5 Coverage Results" MODIFIED="1745310574944" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0602_py$LDM_0602__1_.coverage" NAME="LDM_0602 (1) Coverage Results" MODIFIED="1748936314448" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Novelty_detection/code/backup" />
    <SUITE FILE_PATH="coverage/MSCNN0519_py$MSCNN0602_4.coverage" NAME="MSCNN0602_4 Coverage Results" MODIFIED="1685697322881" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/interpolation0506_py$SRCNN0606.coverage" NAME="SRCNN0606 Coverage Results" MODIFIED="1717665900632" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/interpolation0506_py$swin_transformer0607.coverage" NAME="swin_transformer0607 Coverage Results" MODIFIED="1718013708460" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0516_py$LDM_0516.coverage" NAME="LDM_0516 Coverage Results" MODIFIED="1747628651442" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Novelty_detection/code" />
    <SUITE FILE_PATH="coverage/interpolation0506_py$swin_transformer0614.coverage" NAME="swin_transformer0614 Coverage Results" MODIFIED="1719190153838" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/cnn_transformer1010_py$cnn_transformer1011.coverage" NAME="cnn_transformer1011 Coverage Results" MODIFIED="1728613005926" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0602_py$LDM_0605_V2.coverage" NAME="LDM_0605_V2 Coverage Results" MODIFIED="1749099752312" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Novelty_detection/code" />
    <SUITE FILE_PATH="coverage/MSCNN0731_py$MSCNN0801_10.coverage" NAME="MSCNN0801_10 Coverage Results" MODIFIED="1690895487826" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0602_py$LDM_0609_V2.coverage" NAME="LDM_0609_V2 Coverage Results" MODIFIED="1749459943206" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Novelty_detection/code" />
    <SUITE FILE_PATH="coverage/MSCNN0519_py$MSCNN0730_1.coverage" NAME="MSCNN0730_1 Coverage Results" MODIFIED="1690693923955" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/VAE_0218_py$swin_transformer_0422_V2.coverage" NAME="swin_transformer_0422_V2 Coverage Results" MODIFIED="1747276136076" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/swin_transformer0703_py$gear_time_delay.coverage" NAME="gear_time_delay Coverage Results" MODIFIED="1724677140152" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/interpolation0506_py$transformer0515.coverage" NAME="transformer0515 Coverage Results" MODIFIED="1715744187670" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/interpolation0506_py$swin_transformer0703.coverage" NAME="swin_transformer0703 Coverage Results" MODIFIED="1719997106843" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/swin_transformer_sin0805_py$swin_transformer_syn0925.coverage" NAME="swin_transformer_syn0925 Coverage Results" MODIFIED="1727269780571" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0608_V3_py$LDM_0716_V6.coverage" NAME="LDM_0716_V6 Coverage Results" MODIFIED="1753082488325" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Novelty_detection/code" />
    <SUITE FILE_PATH="coverage/swin_transformer0703_py$swin_transformer0801.coverage" NAME="swin_transformer0801 Coverage Results" MODIFIED="1722482446930" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0418_V1_py$LDM_0418.coverage" NAME="LDM_0418 Coverage Results" MODIFIED="1745054467776" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/swin_transformer0703_py$swin_transformer_sin0805_4.coverage" NAME="swin_transformer_sin0805_4 Coverage Results" MODIFIED="1722864779924" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0609_py$LDM_0716_V7.coverage" NAME="LDM_0716_V7 Coverage Results" MODIFIED="1752742856600" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Novelty_detection/code" />
    <SUITE FILE_PATH="coverage/heatmap_1018_py$heatmap_1018.coverage" NAME="heatmap_1018 Coverage Results" MODIFIED="1741267720810" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/tradition0730_py$tradition0730.coverage" NAME="tradition0730 Coverage Results" MODIFIED="1728357313404" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/interpolation0506_py$swin_transformer0604_3.coverage" NAME="swin_transformer0604_3 Coverage Results" MODIFIED="1717492114364" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0602_py$LDM_0603_V2.coverage" NAME="LDM_0603_V2 Coverage Results" MODIFIED="1748937508427" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Novelty_detection/code" />
    <SUITE FILE_PATH="coverage/interpolation0506_py$Encoder0517.coverage" NAME="Encoder0517 Coverage Results" MODIFIED="1715934927870" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0731_py$transformer0410.coverage" NAME="transformer0410 Coverage Results" MODIFIED="1712824511634" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/Kriging_0423_py$Kriging_0424_V1.coverage" NAME="Kriging_0424_V1 Coverage Results" MODIFIED="1745485792673" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/swin_transformer0703_py$swin_transformer0726.coverage" NAME="swin_transformer0726 Coverage Results" MODIFIED="1721981433251" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/upsampling_data_model" />
    <SUITE FILE_PATH="coverage/VAE_0218_py$tradition0730.coverage" NAME="tradition0730 Coverage Results" MODIFIED="1747273694607" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/interpolation0506_py$swin_transformer0624_3.coverage" NAME="swin_transformer0624_3 Coverage Results" MODIFIED="1719231178669" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0407_py$Condition_cluster0516.coverage" NAME="Condition_cluster0516 Coverage Results" MODIFIED="1747536913273" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Novelty_detection/code" />
    <SUITE FILE_PATH="coverage/MSCNN0731_py$MSCNN0730_1.coverage" NAME="MSCNN0730_1 Coverage Results" MODIFIED="1690791072310" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0519_py$MSCNN0601_1.coverage" NAME="MSCNN0601_1 Coverage Results" MODIFIED="1685603244274" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0519_py$MSCNN0729.coverage" NAME="MSCNN0729 Coverage Results" MODIFIED="1690629809782" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0519_py$MSCNN0731.coverage" NAME="MSCNN0731 Coverage Results" MODIFIED="1690772525720" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/interpolation0506_py$transformer0514.coverage" NAME="transformer0514 Coverage Results" MODIFIED="1715757414258" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0519_py$MSCNN0601.coverage" NAME="MSCNN0601 Coverage Results" MODIFIED="1685592211840" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/swin_transformer0703_py$swin_transformer0730_3.coverage" NAME="swin_transformer0730_3 Coverage Results" MODIFIED="1722417586098" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0731_py$MSCNN0802_6.coverage" NAME="MSCNN0802_6 Coverage Results" MODIFIED="1690964032786" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0519_py$MSCNN0605.coverage" NAME="MSCNN0605 Coverage Results" MODIFIED="1685938662248" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0731_py$MSCNN0802_2.coverage" NAME="MSCNN0802_2 Coverage Results" MODIFIED="1691030958270" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/swin_transformer_syn0925_3_py$tradition_syn.coverage" NAME="tradition_syn Coverage Results" MODIFIED="1727665165591" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/swin_transformer0730_1_py$swin_transformer0730_3.coverage" NAME="swin_transformer0730_3 Coverage Results" MODIFIED="1728203624672" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0609_py$kuangtu.coverage" NAME="kuangtu Coverage Results" MODIFIED="1750385637057" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Novelty_detection/code" />
    <SUITE FILE_PATH="coverage/swin_transformer0703_py$swin_transformer0731.coverage" NAME="swin_transformer0731 Coverage Results" MODIFIED="1722479962661" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/interpolation0506_py$common.coverage" NAME="common Coverage Results" MODIFIED="1719996516334" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="D:/Anaconda3/envs/pytorch/Lib/site-packages/pandas/io" />
    <SUITE FILE_PATH="coverage/MSCNN0519_py$MSCNN0511_1.coverage" NAME="MSCNN0511_1 Coverage Results" MODIFIED="1684648673335" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0731_py$MSCNN0802.coverage" NAME="MSCNN0802 Coverage Results" MODIFIED="1690942873604" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0602_py$LDM_0606.coverage" NAME="LDM_0606 Coverage Results" MODIFIED="1749351845838" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Novelty_detection/code" />
    <SUITE FILE_PATH="coverage/LDM_0418_py$swin_transformer_0422_V8.coverage" NAME="swin_transformer_0422_V8 Coverage Results" MODIFIED="1745372444137" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0519_py$MSCNN0602_3.coverage" NAME="MSCNN0602_3 Coverage Results" MODIFIED="1685695697998" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0404_V2_py$LDM_0408.coverage" NAME="LDM_0408 Coverage Results" MODIFIED="1744100010029" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0418_py$swin_transformer_0422_V1.coverage" NAME="swin_transformer_0422_V1 Coverage Results" MODIFIED="1745306268507" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0519_py$MSCNN0531.coverage" NAME="MSCNN0531 Coverage Results" MODIFIED="1685523233805" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0227_py$LDM_0227.coverage" NAME="LDM_0227 Coverage Results" MODIFIED="1740974563505" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/interpolation0506_py$SRCNN0607.coverage" NAME="SRCNN0607 Coverage Results" MODIFIED="1717728652172" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/interpolation0506_py$FSRCNN0613.coverage" NAME="FSRCNN0613 Coverage Results" MODIFIED="1718285448309" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0418_py$swin_transformer_0422.coverage" NAME="swin_transformer_0422 Coverage Results" MODIFIED="1745291951192" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0519_py$MSCNN0522.coverage" NAME="MSCNN0522 Coverage Results" MODIFIED="1684763661946" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/interpolation0506_py$swin_transformer0625_2.coverage" NAME="swin_transformer0625_2 Coverage Results" MODIFIED="1719302429470" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0519_py$MSCNN0522_1.coverage" NAME="MSCNN0522_1 Coverage Results" MODIFIED="1684766019875" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/swin_transformer_sin0805_py$swin_transformer_syn0922_2.coverage" NAME="swin_transformer_syn0922_2 Coverage Results" MODIFIED="1726997172425" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/interpolation0506_py$SRCNN0613.coverage" NAME="SRCNN0613 Coverage Results" MODIFIED="1718271589937" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0731_py$MSCNN0801_7.coverage" NAME="MSCNN0801_7 Coverage Results" MODIFIED="1690880220051" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0731_py$MSCNN0731_3.coverage" NAME="MSCNN0731_3 Coverage Results" MODIFIED="1690792136961" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/tradition0730_py$FSRCNN0613_2.coverage" NAME="FSRCNN0613_2 Coverage Results" MODIFIED="1728526349502" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0519_py$MSCNN0608_4.coverage" NAME="MSCNN0608_4 Coverage Results" MODIFIED="1686538003952" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0519_py$MSCNN0530_2.coverage" NAME="MSCNN0530_2 Coverage Results" MODIFIED="1685453030415" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0801_py$MSCNN0730.coverage" NAME="MSCNN0730 Coverage Results" MODIFIED="1749611878637" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0418_py$swin_transformer_0422_V4.coverage" NAME="swin_transformer_0422_V4 Coverage Results" MODIFIED="1745309667808" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0731_py$MSCNN0801_11.coverage" NAME="MSCNN0801_11 Coverage Results" MODIFIED="1690897396956" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/heatmap_1018_py$hearmap_detection.coverage" NAME="hearmap_detection Coverage Results" MODIFIED="1741268652428" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/tradition0730_py$FSRCNN1010.coverage" NAME="FSRCNN1010 Coverage Results" MODIFIED="1728545623741" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0519_py$confusion_matrix.coverage" NAME="confusion matrix Coverage Results" MODIFIED="1690631429279" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0716_V7_py$LDM_0721.coverage" NAME="LDM_0721 Coverage Results" MODIFIED="1753166373890" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Novelty_detection/code" />
    <SUITE FILE_PATH="coverage/MSCNN0731_py$MSCNN0917.coverage" NAME="MSCNN0917 Coverage Results" MODIFIED="1694958951010" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/heatmap_1018_py$LDM_0401.coverage" NAME="LDM_0401 Coverage Results" MODIFIED="1743478006369" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0404_V2_py$LDM_0415_V2.coverage" NAME="LDM_0415_V2 Coverage Results" MODIFIED="1744768048742" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/swin_transformer0703_py$swin_transformer0729.coverage" NAME="swin_transformer0729 Coverage Results" MODIFIED="1722224949822" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0529_V4_py$LDM_0602.coverage" NAME="LDM_0602 Coverage Results" MODIFIED="1748838580348" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Novelty_detection/code" />
    <SUITE FILE_PATH="coverage/cnn_transformer1010_py$swin_transformer0806.coverage" NAME="swin_transformer0806 Coverage Results" MODIFIED="1728614847016" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0529_V3_py$LDM_0529_V5.coverage" NAME="LDM_0529_V5 Coverage Results" MODIFIED="1748512239227" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Novelty_detection/code" />
    <SUITE FILE_PATH="coverage/MSCNN0731_py$confusion_matrix.coverage" NAME="confusion matrix Coverage Results" MODIFIED="1696664102646" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0801_py$MSCNN0802_1.coverage" NAME="MSCNN0802_1 Coverage Results" MODIFIED="1749611915843" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0519_py$MSCNN0524_1.coverage" NAME="MSCNN0524_1 Coverage Results" MODIFIED="1684897612364" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/interpolation0506_py$demo.coverage" NAME="demo Coverage Results" MODIFIED="1718162490875" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0731_py$MSCNN0801_4.coverage" NAME="MSCNN0801_4 Coverage Results" MODIFIED="1690862728378" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0801_py$MSCNN0829.coverage" NAME="MSCNN0829 Coverage Results" MODIFIED="1749612101493" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0519_py$MSCNN0703.coverage" NAME="MSCNN0703 Coverage Results" MODIFIED="1688373309800" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0519_py$MSCNN0519.coverage" NAME="MSCNN0519 Coverage Results" MODIFIED="1684761917797" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/VAE_0218_py$VAE_edge_Puchuang.coverage" NAME="VAE_edge_Puchuang Coverage Results" MODIFIED="1747278373861" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Novelty_detection/code" />
    <SUITE FILE_PATH="coverage/MSCNN0519_py$MSCNN0608.coverage" NAME="MSCNN0608 Coverage Results" MODIFIED="1686194401241" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/cnn_transformer1010_py$count_parameter.coverage" NAME="count parameter Coverage Results" MODIFIED="1741248209607" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0519_py$MSCNN0525.coverage" NAME="MSCNN0525 Coverage Results" MODIFIED="1685005532382" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/swin_transformer_syn0930_py$swin_transformer_syn0925_3.coverage" NAME="swin_transformer_syn0925_3 Coverage Results" MODIFIED="1727858639306" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0609_py$LDM_0716_V10.coverage" NAME="LDM_0716_V10 Coverage Results" MODIFIED="1752822670274" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Novelty_detection/code" />
    <SUITE FILE_PATH="coverage/LDM_0602_py$LDM_0608_V4.coverage" NAME="LDM_0608_V4 Coverage Results" MODIFIED="1749373676009" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Novelty_detection/code" />
    <SUITE FILE_PATH="coverage/heatmap_py$heatmap_1018.coverage" NAME="heatmap_1018 Coverage Results" MODIFIED="1729588287430" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/VAE_OCSVM_0203_py$Encoder0517.coverage" NAME="Encoder0517 Coverage Results" MODIFIED="1740366185474" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/VAE_OCSVM_0203_py$LDM_0227.coverage" NAME="LDM_0227 Coverage Results" MODIFIED="1740734289933" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0519_py$MSCNN0607_2.coverage" NAME="MSCNN0607_2 Coverage Results" MODIFIED="1686389039323" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/swin_transformer0703_py$swin_transformer_sin0801.coverage" NAME="swin_transformer_sin0801 Coverage Results" MODIFIED="1722563657053" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/swin_transformer0703_py$swin_transformer0730_1.coverage" NAME="swin_transformer0730_1 Coverage Results" MODIFIED="1722418252723" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/VAE_edge_Puchuang_py$VAE_edge_Puchuang_para.coverage" NAME="VAE_edge_Puchuang_para Coverage Results" MODIFIED="1747280679103" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Novelty_detection/code" />
    <SUITE FILE_PATH="coverage/swin_transformer0703_py$swin_transformer0726__1_.coverage" NAME="swin_transformer0726 (1) Coverage Results" MODIFIED="1721983799149" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/Kriging_0423_py$Kriging_0425.coverage" NAME="Kriging_0425 Coverage Results" MODIFIED="1745566549980" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/heatmap_1018_py$VAE_0218.coverage" NAME="VAE_0218 Coverage Results" MODIFIED="1743125965170" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/pythonProject$main.coverage" NAME="main Coverage Results" MODIFIED="1684413400837" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0609_py$LDM_0716_V4.coverage" NAME="LDM_0716_V4 Coverage Results" MODIFIED="1752831566953" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Novelty_detection/code" />
    <SUITE FILE_PATH="coverage/MSCNN0731_py$MSCNN0918.coverage" NAME="MSCNN0918 Coverage Results" MODIFIED="1695023170032" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/interpolation0506_py$transformer0516_2.coverage" NAME="transformer0516_2 Coverage Results" MODIFIED="1715868336755" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0602_py$LDM_0604.coverage" NAME="LDM_0604 Coverage Results" MODIFIED="1749011004457" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Novelty_detection/code" />
    <SUITE FILE_PATH="coverage/swin_transformer0703_py$tradition_sin0802.coverage" NAME="tradition_sin0802 Coverage Results" MODIFIED="1722570608490" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/swin_transformer_sin0805_py$swin_transformer_syn0922.coverage" NAME="swin_transformer_syn0922 Coverage Results" MODIFIED="1726996921434" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0404_V2_py$LDM_0417_V2.coverage" NAME="LDM_0417_V2 Coverage Results" MODIFIED="1744944953029" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0519_py$MSCNN0523_1.coverage" NAME="MSCNN0523_1 Coverage Results" MODIFIED="1684848406315" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/swin_transformer_syn0930_py$swin_transformer_syn0930.coverage" NAME="swin_transformer_syn0930 Coverage Results" MODIFIED="1727836304302" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/heatmap_py$heatmap_AMFM_SwinT.coverage" NAME="heatmap_AMFM_SwinT Coverage Results" MODIFIED="1729235588794" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/swin_transformer_syn0925_3_py$swin_transformer_syn0925.coverage" NAME="swin_transformer_syn0925 Coverage Results" MODIFIED="1727664843001" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/heatmap_1018_py$demo1.coverage" NAME="demo1 Coverage Results" MODIFIED="1741315900071" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0404_V2_py$LDM_0415_VAE.coverage" NAME="LDM_0415_VAE Coverage Results" MODIFIED="1744702607596" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/upsampling_data_model" />
    <SUITE FILE_PATH="coverage/VAE_OCSVM_0203_py$VAE_OCSVM_0203.coverage" NAME="VAE_OCSVM_0203 Coverage Results" MODIFIED="1739849608689" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/interpolation0506_py$try0515_.coverage" NAME="try0515' Coverage Results" MODIFIED="1715744789598" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0608_V3_py$LDM_0716_V4.coverage" NAME="LDM_0716_V4 Coverage Results" MODIFIED="1753066736683" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Novelty_detection/code" />
    <SUITE FILE_PATH="coverage/MSCNN0731_py$MSCNN0801_12.coverage" NAME="MSCNN0801_12 Coverage Results" MODIFIED="1690899366105" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0519_py$MSCNN0608_1.coverage" NAME="MSCNN0608_1 Coverage Results" MODIFIED="1686214613547" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/VAE_OCSVM_0203_py$LDM_0220.coverage" NAME="LDM_0220 Coverage Results" MODIFIED="1740040146017" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/swin_transformer0703_py$tradition_AMESim.coverage" NAME="tradition_AMESim Coverage Results" MODIFIED="1723714082680" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0418_V1_py$LDM_0407.coverage" NAME="LDM_0407 Coverage Results" MODIFIED="1745045735979" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0609_py$LDM_0611.coverage" NAME="LDM_0611 Coverage Results" MODIFIED="1749779640333" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Novelty_detection/code" />
    <SUITE FILE_PATH="coverage/VAE_0218_py$LDM_0407.coverage" NAME="LDM_0407 Coverage Results" MODIFIED="1747277260134" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Novelty_detection/code" />
    <SUITE FILE_PATH="coverage/swin_transformer0703_py$tradition_gear.coverage" NAME="tradition_gear Coverage Results" MODIFIED="1724663560415" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0731_py$MSCNN1007.coverage" NAME="MSCNN1007 Coverage Results" MODIFIED="1696649278108" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0404_V2_py$LDM_0418.coverage" NAME="LDM_0418 Coverage Results" MODIFIED="1744963983111" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0731_py$MSCNN0731_2.coverage" NAME="MSCNN0731_2 Coverage Results" MODIFIED="1690791891742" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/swin_transformer0703_py$swin_transformer0806_1.coverage" NAME="swin_transformer0806_1 Coverage Results" MODIFIED="1723086043907" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0519_py$MSCNN0607_5.coverage" NAME="MSCNN0607_5 Coverage Results" MODIFIED="1686123630343" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0731_py$MSCNN0612_2.coverage" NAME="MSCNN0612_2 Coverage Results" MODIFIED="1709625817181" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/swin_transformer0703_py$swin_transformer0725.coverage" NAME="swin_transformer0725 Coverage Results" MODIFIED="1722222482724" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0519_py$MSCNN0523.coverage" NAME="MSCNN0523 Coverage Results" MODIFIED="1684823964978" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0519_py$MSCNN0602_1.coverage" NAME="MSCNN0602_1 Coverage Results" MODIFIED="1685676572625" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0519_py$MSCNN0612_1.coverage" NAME="MSCNN0612_1 Coverage Results" MODIFIED="1690690655417" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/interpolation0506_py$swin_transformer0625_3.coverage" NAME="swin_transformer0625_3 Coverage Results" MODIFIED="1719802856446" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/swin_transformer0703_py$swin_transformer_sin0805_2.coverage" NAME="swin_transformer_sin0805_2 Coverage Results" MODIFIED="1722861721210" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/VAE_OCSVM_0203_py$VAE_0218.coverage" NAME="VAE_0218 Coverage Results" MODIFIED="1740366219288" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0731_py$MSCNN0731_7.coverage" NAME="MSCNN0731_7 Coverage Results" MODIFIED="1690808160365" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/tradition0730_py$swin_transformer0806_1.coverage" NAME="swin_transformer0806_1 Coverage Results" MODIFIED="1728447109948" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0521_py$LDM_0529_V2.coverage" NAME="LDM_0529_V2 Coverage Results" MODIFIED="1748504381119" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Novelty_detection/code" />
    <SUITE FILE_PATH="coverage/LDM_0418_py$Kriging_0423.coverage" NAME="Kriging_0423 Coverage Results" MODIFIED="1745393155542" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/swin_transformer_syn0925_3_py$swin_transformer_syn0925_2.coverage" NAME="swin_transformer_syn0925_2 Coverage Results" MODIFIED="1727663742131" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/VAE_0218_py$swin_transformer_Puchuang.coverage" NAME="swin_transformer_Puchuang Coverage Results" MODIFIED="1747276307070" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0519_py$MSCNN0613_1.coverage" NAME="MSCNN0613_1 Coverage Results" MODIFIED="1688374061468" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/swin_transformer0703_py$swin_transformer0728_2.coverage" NAME="swin_transformer0728_2 Coverage Results" MODIFIED="1722216956484" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/heatmap_1018_py$LDM_0227.coverage" NAME="LDM_0227 Coverage Results" MODIFIED="1742883883621" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0609_py$LDM_0613.coverage" NAME="LDM_0613 Coverage Results" MODIFIED="1749798563283" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Novelty_detection/code" />
    <SUITE FILE_PATH="coverage/Kriging_0423_py$tradition_0428.coverage" NAME="tradition_0428 Coverage Results" MODIFIED="1745931323453" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0602_py$LDM_0609.coverage" NAME="LDM_0609 Coverage Results" MODIFIED="1749463353435" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Novelty_detection/code" />
    <SUITE FILE_PATH="coverage/MSCNN0731_py$MSCNN0731_9.coverage" NAME="MSCNN0731_9 Coverage Results" MODIFIED="1690813298077" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/tradition0730_py$swin_transformer0607.coverage" NAME="swin_transformer0607 Coverage Results" MODIFIED="1728529982101" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/VAE_edge_Puchuang_py$swin_transformer_Puchuang.coverage" NAME="swin_transformer_Puchuang Coverage Results" MODIFIED="1747282419667" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/tradition_0428_py$tradition0730.coverage" NAME="tradition0730 Coverage Results" MODIFIED="1746582819367" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0731_py$MSCNN0918_1.coverage" NAME="MSCNN0918_1 Coverage Results" MODIFIED="1695348725361" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0731_py$MSCNN0922_1.coverage" NAME="MSCNN0922_1 Coverage Results" MODIFIED="1695372039735" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0609_py$demo_noise.coverage" NAME="demo_noise Coverage Results" MODIFIED="1750063327997" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Novelty_detection/code" />
    <SUITE FILE_PATH="coverage/interpolation0506_py$sub_pixel_convolution0424.coverage" NAME="sub_pixel convolution0424 Coverage Results" MODIFIED="1715766239940" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0516_py$LDM_0521.coverage" NAME="LDM_0521 Coverage Results" MODIFIED="1747985765394" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Novelty_detection/code" />
    <SUITE FILE_PATH="coverage/MSCNN0519_py$MSCNN0525_2.coverage" NAME="MSCNN0525_2 Coverage Results" MODIFIED="1685192354939" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0609_py$LDM_0716_V11.coverage" NAME="LDM_0716_V11 Coverage Results" MODIFIED="1752823977092" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Novelty_detection/code" />
    <SUITE FILE_PATH="coverage/MSCNN0731_py$MSCNN0802_5.coverage" NAME="MSCNN0802_5 Coverage Results" MODIFIED="1690964428573" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/VAE_edge_Puchuang_py$swin_transformer_Puchuang_para__1_.coverage" NAME="swin_transformer_Puchuang_para (1) Coverage Results" MODIFIED="1747282612830" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Novelty_detection/code" />
    <SUITE FILE_PATH="coverage/LDM_0418_py$swin_transformer0730_1.coverage" NAME="swin_transformer0730_1 Coverage Results" MODIFIED="1745291851133" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/Kriging_0423_py$Kriging_0423.coverage" NAME="Kriging_0423 Coverage Results" MODIFIED="1745478708329" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/swin_transformer_sin0805_py$tradition_syn.coverage" NAME="tradition_syn Coverage Results" MODIFIED="1727315931445" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0731_py$MSCNN0802_3.coverage" NAME="MSCNN0802_3 Coverage Results" MODIFIED="1714016828261" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0731_py$MSCNN0801_5.coverage" NAME="MSCNN0801_5 Coverage Results" MODIFIED="1690876188898" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0516_py$LDM_0523.coverage" NAME="LDM_0523 Coverage Results" MODIFIED="1747969196951" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Novelty_detection/code" />
    <SUITE FILE_PATH="coverage/interpolation0506_py$interpolation0508.coverage" NAME="interpolation0508 Coverage Results" MODIFIED="1715159438499" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0404_V2_py$LDM_0407_V2.coverage" NAME="LDM_0407_V2 Coverage Results" MODIFIED="1744943432635" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/cnn_transformer1010_py$sin_cos_noise.coverage" NAME="sin_cos_noise Coverage Results" MODIFIED="1729151115590" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0519_py$MSCNN0613.coverage" NAME="MSCNN0613 Coverage Results" MODIFIED="1688373937443" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/interpolation0506_py$interpolation0514.coverage" NAME="interpolation0514 Coverage Results" MODIFIED="1715691855233" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0519_py$MSCNN0521.coverage" NAME="MSCNN0521 Coverage Results" MODIFIED="1684657013858" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0519_py$MCNN0425.coverage" NAME="MCNN0425 Coverage Results" MODIFIED="1685522167773" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/interpolation0506_py$swin_transformer0624.coverage" NAME="swin_transformer0624 Coverage Results" MODIFIED="1719219586144" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0519_py$MSCNN0506_2.coverage" NAME="MSCNN0506_2 Coverage Results" MODIFIED="1685940273404" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0519_py$MSCNN0518.coverage" NAME="MSCNN0518 Coverage Results" MODIFIED="1685629679044" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/interpolation0506_py$transformer0515_2.coverage" NAME="transformer0515_2 Coverage Results" MODIFIED="1715822150645" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0519_py$MSCNN0607_3.coverage" NAME="MSCNN0607_3 Coverage Results" MODIFIED="1686121138911" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0731_py$heatmap.coverage" NAME="heatmap Coverage Results" MODIFIED="1693382112300" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0404_V2_py$LDM_0404_V2.coverage" NAME="LDM_0404_V2 Coverage Results" MODIFIED="1743753067142" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/swin_transformer_sin0805_py$swin_transformer_syn0925_3.coverage" NAME="swin_transformer_syn0925_3 Coverage Results" MODIFIED="1727315024237" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/interpolation0506_py$swin_transformer0606_1.coverage" NAME="swin_transformer0606_1 Coverage Results" MODIFIED="1717660243981" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0519_py$MSCNN0608_3.coverage" NAME="MSCNN0608_3 Coverage Results" MODIFIED="1688373660867" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/interpolation0506_py$tradition0606.coverage" NAME="tradition0606 Coverage Results" MODIFIED="1718282200500" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/swin_transformer0703_py$swin_transformer0728.coverage" NAME="swin_transformer0728 Coverage Results" MODIFIED="1722162655346" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/interpolation0506_py$swin_transformer0613.coverage" NAME="swin_transformer0613 Coverage Results" MODIFIED="1718330311260" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0731_py$MSCNN0918_3.coverage" NAME="MSCNN0918_3 Coverage Results" MODIFIED="1695302304534" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0731_py$MSCNN0731_4.coverage" NAME="MSCNN0731_4 Coverage Results" MODIFIED="1690792205664" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0407_py$LDM_0407.coverage" NAME="LDM_0407 Coverage Results" MODIFIED="1747380092822" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Novelty_detection/code" />
    <SUITE FILE_PATH="coverage/swin_transformer_sin0805_py$tradition_sin_noise.coverage" NAME="tradition_sin_noise Coverage Results" MODIFIED="1727595167593" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0519_py$MSCNN0524_2.coverage" NAME="MSCNN0524_2 Coverage Results" MODIFIED="1684914417990" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0519_py$MSCNN0606.coverage" NAME="MSCNN0606 Coverage Results" MODIFIED="1686061249765" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0404_V2_py$LDM_VAE_0411.coverage" NAME="LDM_VAE_0411 Coverage Results" MODIFIED="1744945470560" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0519_py$MSCNN0608_5.coverage" NAME="MSCNN0608_5 Coverage Results" MODIFIED="1686622815028" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0602_py$LDM_0606_V2.coverage" NAME="LDM_0606_V2 Coverage Results" MODIFIED="1749354781223" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Novelty_detection/code" />
    <SUITE FILE_PATH="coverage/MSCNN0731_py$MSCNN0802_1.coverage" NAME="MSCNN0802_1 Coverage Results" MODIFIED="1690944340667" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0731_py$MSCNN0801_3.coverage" NAME="MSCNN0801_3 Coverage Results" MODIFIED="1690968161709" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0801_py$MSCNN0729.coverage" NAME="MSCNN0729 Coverage Results" MODIFIED="1749612575397" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0401_V2_py$LDM_0404.coverage" NAME="LDM_0404 Coverage Results" MODIFIED="1743732951586" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0519_py$MSCNN0607_7.coverage" NAME="MSCNN0607_7 Coverage Results" MODIFIED="1686198136868" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0519_py$MSCNN0530_4.coverage" NAME="MSCNN0530_4 Coverage Results" MODIFIED="1685451590580" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0529_V3_py$LDM_0529_V6.coverage" NAME="LDM_0529_V6 Coverage Results" MODIFIED="1748524681695" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Novelty_detection/code" />
    <SUITE FILE_PATH="coverage/swin_transformer0703_py$swin_transformer_gear0825.coverage" NAME="swin_transformer_gear0825 Coverage Results" MODIFIED="1724723525869" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/VAE_OCSVM_0203_py$LDM_0228.coverage" NAME="LDM_0228 Coverage Results" MODIFIED="1740734231392" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0418_py$cnn_transformer1010.coverage" NAME="cnn_transformer1010 Coverage Results" MODIFIED="1745291192676" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0731_py$MSCNN0801.coverage" NAME="MSCNN0801 Coverage Results" MODIFIED="1690855614073" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/swin_transformer0703_py$tradition_sin0805.coverage" NAME="tradition_sin0805 Coverage Results" MODIFIED="1722912049530" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0519_py$MSCNN0530.coverage" NAME="MSCNN0530 Coverage Results" MODIFIED="1685437969067" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0519_py$MSCNN0607_1.coverage" NAME="MSCNN0607_1 Coverage Results" MODIFIED="1686109365566" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0602_py$LDM_0604_V2.coverage" NAME="LDM_0604_V2 Coverage Results" MODIFIED="1749107558356" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Novelty_detection/code" />
    <SUITE FILE_PATH="coverage/heatmap_1018_py$LDM_0326.coverage" NAME="LDM_0326 Coverage Results" MODIFIED="1743321013167" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/swin_transformer0703_py$swin_transformer0730.coverage" NAME="swin_transformer0730 Coverage Results" MODIFIED="1722310205773" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0801_py$MSCNN0802.coverage" NAME="MSCNN0802 Coverage Results" MODIFIED="1749611977592" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0516_py$LDM_0520.coverage" NAME="LDM_0520 Coverage Results" MODIFIED="1747728871852" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Novelty_detection/code" />
    <SUITE FILE_PATH="coverage/MSCNN0519_py$MSCNN0527.coverage" NAME="MSCNN0527 Coverage Results" MODIFIED="1685190054390" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/cnn_transformer1010_py$cnn_transformer1010.coverage" NAME="cnn_transformer1010 Coverage Results" MODIFIED="1728629474960" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0418_py$swin_transformer_0422_V2.coverage" NAME="swin_transformer_0422_V2 Coverage Results" MODIFIED="1745304518215" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0731_py$MSCNN0731_10.coverage" NAME="MSCNN0731_10 Coverage Results" MODIFIED="1690813217588" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0404_V2_py$LDM_conditionclassifier_0411.coverage" NAME="LDM_conditionclassifier_0411 Coverage Results" MODIFIED="1744767599265" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0731_py$MSCNN0730.coverage" NAME="MSCNN0730 Coverage Results" MODIFIED="1690790751561" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0609_py$LDM_0716_V12.coverage" NAME="LDM_0716_V12 Coverage Results" MODIFIED="1752825405064" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Novelty_detection/code" />
    <SUITE FILE_PATH="coverage/MSCNN0519_py$MSCNN0605_4.coverage" NAME="MSCNN0605_4 Coverage Results" MODIFIED="1686035475458" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/interpolation0506_py$swin_transformer0604_2.coverage" NAME="swin_transformer0604_2 Coverage Results" MODIFIED="1717645890711" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0731_py$MSCNN1006_1.coverage" NAME="MSCNN1006_1 Coverage Results" MODIFIED="1696663575221" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0404_V2_py$MSCNN0530_1.coverage" NAME="MSCNN0530_1 Coverage Results" MODIFIED="1744335497962" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0418_py$swin_transformer_0422_V3.coverage" NAME="swin_transformer_0422_V3 Coverage Results" MODIFIED="1745308024344" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0519_py$MSCNN0608_6.coverage" NAME="MSCNN0608_6 Coverage Results" MODIFIED="1686621790535" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0407_py$LDM_0516.coverage" NAME="LDM_0516 Coverage Results" MODIFIED="1747382517862" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Novelty_detection/code" />
    <SUITE FILE_PATH="coverage/heatmap_1018_py$LDM_0327.coverage" NAME="LDM_0327 Coverage Results" MODIFIED="1743429994197" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0731_py$MSCNN0731.coverage" NAME="MSCNN0731 Coverage Results" MODIFIED="1690791459316" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/interpolation0506_py$swin_transformer0624_2.coverage" NAME="swin_transformer0624_2 Coverage Results" MODIFIED="1719220568211" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/interpolation0506_py$SRCNN0606_2.coverage" NAME="SRCNN0606_2 Coverage Results" MODIFIED="1717725627663" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0404_V2_py$LDM_0407.coverage" NAME="LDM_0407 Coverage Results" MODIFIED="1744085836393" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0519_py$MSCNN0521_1.coverage" NAME="MSCNN0521_1 Coverage Results" MODIFIED="1684679006099" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0609_py$LDM_0716_V6.coverage" NAME="LDM_0716_V6 Coverage Results" MODIFIED="1752740303042" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Novelty_detection/code" />
    <SUITE FILE_PATH="coverage/MSCNN0519_py$MSCNN0612_3.coverage" NAME="MSCNN0612_3 Coverage Results" MODIFIED="1686561509008" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/tradition0730_py$swin_transformer0806.coverage" NAME="swin_transformer0806 Coverage Results" MODIFIED="1728446978585" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0519_py$MSCNN0730.coverage" NAME="MSCNN0730 Coverage Results" MODIFIED="1690691417932" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/swin_transformer0703_py$swin_transformer0725__1_.coverage" NAME="swin_transformer0725 (1) Coverage Results" MODIFIED="1722161193276" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0521_py$LDM_0529_V3.coverage" NAME="LDM_0529_V3 Coverage Results" MODIFIED="1748509209719" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Novelty_detection/code" />
    <SUITE FILE_PATH="coverage/heatmap_py$FSRCNN1010.coverage" NAME="FSRCNN1010 Coverage Results" MODIFIED="1733218798454" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0602_py$LDM_0603_V4.coverage" NAME="LDM_0603_V4 Coverage Results" MODIFIED="1749107955600" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Novelty_detection/code" />
    <SUITE FILE_PATH="coverage/MSCNN0731_py$MSCNN0928.coverage" NAME="MSCNN0928 Coverage Results" MODIFIED="1695889108353" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0716_V7_py$LDM_0721_V2.coverage" NAME="LDM_0721_V2 Coverage Results" MODIFIED="1753171315705" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Novelty_detection/code" />
    <SUITE FILE_PATH="coverage/interpolation0506_py$swin_transformer0612.coverage" NAME="swin_transformer0612 Coverage Results" MODIFIED="1718263557807" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0227_py$LDM_OCSVM_0303.coverage" NAME="LDM_OCSVM_0303 Coverage Results" MODIFIED="1740974449018" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0418_V1_py$LDM_0418_V1.coverage" NAME="LDM_0418_V1 Coverage Results" MODIFIED="1745034245589" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0731_py$flow_ripple1205.coverage" NAME="flow_ripple1205 Coverage Results" MODIFIED="1714702445259" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/swin_transformer0703_py$swin_transformer0730_2.coverage" NAME="swin_transformer0730_2 Coverage Results" MODIFIED="1722498452545" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0731_py$MSCNN0801_2.coverage" NAME="MSCNN0801_2 Coverage Results" MODIFIED="1691032327243" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/interpolation0506_py$swin_transformer0701.coverage" NAME="swin_transformer0701 Coverage Results" MODIFIED="1719996522387" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0731_py$MSCNN0731_5.coverage" NAME="MSCNN0731_5 Coverage Results" MODIFIED="1690804453863" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/interpolation0506_py$SRCNN0516.coverage" NAME="SRCNN0516 Coverage Results" MODIFIED="1717667665863" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0609_py$LDM_0716_V2.coverage" NAME="LDM_0716_V2 Coverage Results" MODIFIED="1752724862482" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Novelty_detection/code" />
    <SUITE FILE_PATH="coverage/interpolation0506_py$FSRCNN0613_2.coverage" NAME="FSRCNN0613_2 Coverage Results" MODIFIED="1719185949994" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/swin_transformer0703_py$swin_transformer_AMESim.coverage" NAME="swin_transformer_AMESim Coverage Results" MODIFIED="1723727888394" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/tradition0730_py$Encoder1010.coverage" NAME="Encoder1010 Coverage Results" MODIFIED="1728530510158" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0731_py$MSCNN0918_2.coverage" NAME="MSCNN0918_2 Coverage Results" MODIFIED="1695348309122" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/heatmap_py$sin_cos_noise.coverage" NAME="sin_cos_noise Coverage Results" MODIFIED="1729474016223" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0602_py$LDM_0608.coverage" NAME="LDM_0608 Coverage Results" MODIFIED="1749372199648" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Novelty_detection/code" />
    <SUITE FILE_PATH="coverage/interpolation0506_py$transformer0517_3.coverage" NAME="transformer0517_3 Coverage Results" MODIFIED="1716169626746" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/swin_transformer0703_py$tradition0730.coverage" NAME="tradition0730 Coverage Results" MODIFIED="1723014786684" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/tradition0730_py$Encoder0517.coverage" NAME="Encoder0517 Coverage Results" MODIFIED="1728530029895" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0418_py$swin_transformer_0422_V6.coverage" NAME="swin_transformer_0422_V6 Coverage Results" MODIFIED="1745371888438" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/swin_transformer_syn0925_3_py$swin_transformer_syn0930.coverage" NAME="swin_transformer_syn0930 Coverage Results" MODIFIED="1727699120620" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/swin_transformer0703_py$swin_transformer0801_1.coverage" NAME="swin_transformer0801_1 Coverage Results" MODIFIED="1722481114506" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/swin_transformer0703_py$tradition_sin.coverage" NAME="tradition_sin Coverage Results" MODIFIED="1723714895847" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0404_V2_py$LDM_0416.coverage" NAME="LDM_0416 Coverage Results" MODIFIED="1744871286991" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0609_py$LDM_0716.coverage" NAME="LDM_0716 Coverage Results" MODIFIED="1752721920396" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Novelty_detection/code" />
    <SUITE FILE_PATH="coverage/interpolation0506_py$transformer0516.coverage" NAME="transformer0516 Coverage Results" MODIFIED="1715852305748" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/interpolation0506_py$swin_transformer0625.coverage" NAME="swin_transformer0625 Coverage Results" MODIFIED="1719285805671" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/swin_transformer0703_py$swin_transformer0705.coverage" NAME="swin_transformer0705 Coverage Results" MODIFIED="1721891212105" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/upsampling_data_model" />
    <SUITE FILE_PATH="coverage/MSCNN0519_py$MSCNN0601_3.coverage" NAME="MSCNN0601_3 Coverage Results" MODIFIED="1685615036531" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/swin_transformer0703_py$swin_transformer_sin0806.coverage" NAME="swin_transformer_sin0806 Coverage Results" MODIFIED="1722911768318" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0609_py$LDM_0716_V9.coverage" NAME="LDM_0716_V9 Coverage Results" MODIFIED="1752745361577" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Novelty_detection/code" />
    <SUITE FILE_PATH="coverage/LDM_0401_V2_py$LDM_0404_V2.coverage" NAME="LDM_0404_V2 Coverage Results" MODIFIED="1743740373604" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0519_py$MSCNN0525_1.coverage" NAME="MSCNN0525_1 Coverage Results" MODIFIED="1684999490619" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/swin_transformer_sin0805_py$swin_transformer_syn0925_2.coverage" NAME="swin_transformer_syn0925_2 Coverage Results" MODIFIED="1727315958620" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/interpolation0506_py$FSRCNN0624.coverage" NAME="FSRCNN0624 Coverage Results" MODIFIED="1719191442136" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/interpolation0506_py$interpolation0509.coverage" NAME="interpolation0509 Coverage Results" MODIFIED="1715657721946" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/swin_transformer0703_py$swin_transformer0728_1.coverage" NAME="swin_transformer0728_1 Coverage Results" MODIFIED="1722164912770" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/MSCNN0731_py$MSCNN0801_9.coverage" NAME="MSCNN0801_9 Coverage Results" MODIFIED="1694954893451" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/LDM_0227_py$VAE_0218.coverage" NAME="VAE_0218 Coverage Results" MODIFIED="1741232340457" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/interpolation0506_py$swin_transformer0701_2.coverage" NAME="swin_transformer0701_2 Coverage Results" MODIFIED="1719803057755" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/swin_transformer_syn0925_3_py$swin_transformer_syn0925_3.coverage" NAME="swin_transformer_syn0925_3 Coverage Results" MODIFIED="1727681258825" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
  </component>
</project>