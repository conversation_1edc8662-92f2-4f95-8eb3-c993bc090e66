import torch
import torch.nn as nn
import torch.nn.functional as F
from torch import optim
import numpy as np
import scipy.io as scio
from torch.utils.data import Dataset, DataLoader, TensorDataset
from visdom import Visdom
import matplotlib.pyplot as plt
from sklearn.metrics import roc_curve, auc, confusion_matrix, classification_report, precision_recall_curve, f1_score
import pandas as pd
from scipy.stats import gaussian_kde  # 用于计算密度估计
from scipy.stats import norm
import json
import math
from collections import Counter

# python -m visdom.server

# 设备配置
device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")

# 实例化一个窗口用于绘制 VAE 训练曲线
VAE_train_wind = Visdom()

# 初始化窗口参数
VAE_train_wind.line([0.0],  # Y的第一个点坐标
                    [0.0],  # X的第一个点坐标
                    win='VAE_train',  # 窗口的名称
                    opts=dict(title='VAE Training Loss', legend=['Loss'])  # 图像的图例
                    )

# VAE 重构误差曲线
VAE_recon_loss = Visdom()

VAE_recon_loss.line([0.0],  # Y的第一个点坐标
                    [0.0],  # X的第一个点坐标
                    win='VAE_recon_loss',  # 窗口的名称
                    opts=dict(title='VAE_recon_loss', legend=['Recon Loss'])  # 图像的图例
                    )

# VAE KL散度曲线
VAE_KL = Visdom()

VAE_KL.line([0.0],  # Y的第一个点坐标
            [0.0],  # X的第一个点坐标
            win='VAE_KL',  # 窗口的名称
            opts=dict(title='VAE_KL', legend=['KL Loss'])  # 图像的图例
            )

# 实例化一个Diffusion训练窗口
Diffusion_train_wind = Visdom()
# 初始化窗口参数
Diffusion_train_wind.line([0.0],  # Y的第一个点坐标
                          [0.0],  # X的第一个点坐标
                          win='Diffusion_train',  # 窗口的名称
                          opts=dict(title='Diffusion Training Loss', legend=['Loss'])  # 图像的图例
                          )


# 一维数据转成二维作为输入
# 定义训练集
class Looseslipper_TrainDataset(Dataset):
    def __init__(self):
        # 训练集均为正常数据
        train_normal_1500_9 = scio.loadmat('..\\dataset\\detection_normal_1500_9_train_2048.mat')
        normal_1500_9 = train_normal_1500_9['detection_normal_1500_9_train_2048']

        self.normal_data_1500_9 = normal_1500_9[0, 0:1843200]  # 样本数：900

        self.normal_data_1500_9 = torch.from_numpy(self.normal_data_1500_9)

        self.normal_data_1500_9 = self.normal_data_1500_9.view(-1, 1, 1, 2048).to(torch.float32)  # [B, C, H, W]

        train_normal_1500_12 = scio.loadmat('..\\dataset\\detection_normal_1500_12_train_2048.mat')
        normal_1500_12 = train_normal_1500_12['detection_normal_1500_12_train_2048']

        self.normal_data_1500_12 = normal_1500_12[0, 0:1843200]  # 样本数：900

        self.normal_data_1500_12 = torch.from_numpy(self.normal_data_1500_12)

        self.normal_data_1500_12 = self.normal_data_1500_12.view(-1, 1, 1, 2048).to(torch.float32)  # [B, C, H, W]

        train_normal_1500_15 = scio.loadmat('..\\dataset\\detection_normal_1500_15_train_2048.mat')
        normal_1500_15 = train_normal_1500_15['detection_normal_1500_15_train_2048']

        self.normal_data_1500_15 = normal_1500_15[0, 0:1843200]  # 样本数：900

        self.normal_data_1500_15 = torch.from_numpy(self.normal_data_1500_15)

        self.normal_data_1500_15 = self.normal_data_1500_15.view(-1, 1, 1, 2048).to(torch.float32)  # [B, C, H, W]

        train_normal_1500_18 = scio.loadmat('..\\dataset\\detection_normal_1500_18_train_2048_NEW1.mat')
        normal_1500_18 = train_normal_1500_18['detection_normal_1500_18_train_2048_NEW1']

        self.normal_data_1500_18 = normal_1500_18[0, 0:1843200]  # 样本数：900

        self.normal_data_1500_18 = torch.from_numpy(self.normal_data_1500_18)

        self.normal_data_1500_18 = self.normal_data_1500_18.view(-1, 1, 1, 2048).to(torch.float32)  # [B, C, H, W]

        train_normal_1200_9 = scio.loadmat('..\\dataset\\detection_normal_1200_9_train_2048.mat')
        normal_1200_9 = train_normal_1200_9['detection_normal_1200_9_train_2048']

        self.normal_data_1200_9 = normal_1200_9[0, 0:1843200]  # 样本数：900

        self.normal_data_1200_9 = torch.from_numpy(self.normal_data_1200_9)

        self.normal_data_1200_9 = self.normal_data_1200_9.view(-1, 1, 1, 2048).to(torch.float32)  # [B, C, H, W]

        train_normal_1200_12 = scio.loadmat('..\\dataset\\detection_normal_1200_12_train_2048.mat')
        normal_1200_12 = train_normal_1200_12['detection_normal_1200_12_train_2048']

        self.normal_data_1200_12 = normal_1200_12[0, 0:1843200]  # 样本数：900

        self.normal_data_1200_12 = torch.from_numpy(self.normal_data_1200_12)

        self.normal_data_1200_12 = self.normal_data_1200_12.view(-1, 1, 1, 2048).to(torch.float32)  # [B, C, H, W]

        train_normal_1200_15 = scio.loadmat('..\\dataset\\detection_normal_1200_15_train_2048.mat')
        normal_1200_15 = train_normal_1200_15['detection_normal_1200_15_train_2048']

        self.normal_data_1200_15 = normal_1200_15[0, 0:1843200]  # 样本数：900

        self.normal_data_1200_15 = torch.from_numpy(self.normal_data_1200_15)

        self.normal_data_1200_15 = self.normal_data_1200_15.view(-1, 1, 1, 2048).to(torch.float32)  # [B, C, H, W]

        train_normal_1200_18 = scio.loadmat('..\\dataset\\detection_normal_1200_18_train_2048_NEW.mat')
        normal_1200_18 = train_normal_1200_18['detection_normal_1200_18_train_2048_NEW']

        self.normal_data_1200_18 = normal_1200_18[0, 0:1843200]  # 样本数：900

        self.normal_data_1200_18 = torch.from_numpy(self.normal_data_1200_18)

        self.normal_data_1200_18 = self.normal_data_1200_18.view(-1, 1, 1, 2048).to(torch.float32)  # [B, C, H, W]

        train_normal_900_9 = scio.loadmat('..\\dataset\\detection_normal_900_9_train_2048.mat')
        normal_900_9 = train_normal_900_9['detection_normal_900_9_train_2048']

        self.normal_data_900_9 = normal_900_9[0, 0:1843200]  # 样本数：900

        self.normal_data_900_9 = torch.from_numpy(self.normal_data_900_9)

        self.normal_data_900_9 = self.normal_data_900_9.view(-1, 1, 1, 2048).to(torch.float32)  # [B, C, H, W]

        train_normal_900_12 = scio.loadmat('..\\dataset\\detection_normal_900_12_train_2048.mat')
        normal_900_12 = train_normal_900_12['detection_normal_900_12_train_2048']

        self.normal_data_900_12 = normal_900_12[0, 0:1843200]  # 样本数：900

        self.normal_data_900_12 = torch.from_numpy(self.normal_data_900_12)

        self.normal_data_900_12 = self.normal_data_900_12.view(-1, 1, 1, 2048).to(torch.float32)  # [B, C, H, W]

        train_normal_900_15 = scio.loadmat('..\\dataset\\detection_normal_900_15_train_2048.mat')
        normal_900_15 = train_normal_900_15['detection_normal_900_15_train_2048']

        self.normal_data_900_15 = normal_900_15[0, 0:1843200]  # 样本数：900

        self.normal_data_900_15 = torch.from_numpy(self.normal_data_900_15)

        self.normal_data_900_15 = self.normal_data_900_15.view(-1, 1, 1, 2048).to(torch.float32)  # [B, C, H, W]

        train_normal_900_18 = scio.loadmat('..\\dataset\\detection_normal_900_18_train_2048.mat')
        normal_900_18 = train_normal_900_18['detection_normal_900_18_train_2048']

        self.normal_data_900_18 = normal_900_18[0, 0:1843200]  # 样本数：900

        self.normal_data_900_18 = torch.from_numpy(self.normal_data_900_18)

        self.normal_data_900_18 = self.normal_data_900_18.view(-1, 1, 1, 2048).to(torch.float32)  # [B, C, H, W]

        self.x_data = [self.normal_data_1500_9, self.normal_data_1500_12, self.normal_data_1500_15,
                       self.normal_data_1500_18,
                       self.normal_data_1200_9, self.normal_data_1200_12, self.normal_data_1200_15,
                       self.normal_data_1200_18,
                       self.normal_data_900_9, self.normal_data_900_12, self.normal_data_900_15,
                       self.normal_data_900_18]
        self.x_data = torch.cat(self.x_data, dim=0)

        size = int(self.x_data.shape[0])  # 计算标签数量
        y_data1 = 0 * np.ones(size)  # 正常数据标签，0
        self.y_data = torch.from_numpy(y_data1)  # 标签转为张量

        # 创建工况标签 (转速, 压力)
        count_1500_9 = self.normal_data_1500_9.shape[0]
        count_1500_12 = self.normal_data_1500_12.shape[0]
        count_1500_15 = self.normal_data_1500_15.shape[0]
        count_1500_18 = self.normal_data_1500_18.shape[0]

        count_1200_9 = self.normal_data_1200_9.shape[0]
        count_1200_12 = self.normal_data_1200_12.shape[0]
        count_1200_15 = self.normal_data_1200_15.shape[0]
        count_1200_18 = self.normal_data_1200_18.shape[0]

        count_900_9 = self.normal_data_900_9.shape[0]
        count_900_12 = self.normal_data_900_12.shape[0]
        count_900_15 = self.normal_data_900_15.shape[0]
        count_900_18 = self.normal_data_900_18.shape[0]

        condition_1500_9 = [(1500, 9)] * count_1500_9  # 转速1500rpm, 压力9MPa
        condition_1500_12 = [(1500, 12)] * count_1500_12  # 转速1500rpm, 压力12MPa
        condition_1500_15 = [(1500, 15)] * count_1500_15  # 转速1500rpm, 压力15MPa
        condition_1500_18 = [(1500, 18)] * count_1500_18  # 转速1500rpm, 压力18MPa

        condition_1200_9 = [(1200, 9)] * count_1200_9  # 转速1200rpm, 压力9MPa
        condition_1200_12 = [(1200, 12)] * count_1200_12  # 转速1200rpm, 压力12MPa
        condition_1200_15 = [(1200, 15)] * count_1200_15  # 转速1200rpm, 压力15MPa
        condition_1200_18 = [(1200, 18)] * count_1200_18  # 转速1200rpm, 压力18MPa

        condition_900_9 = [(900, 9)] * count_900_9  # 转速900rpm, 压力9MPa
        condition_900_12 = [(900, 12)] * count_900_12  # 转速900rpm, 压力12MPa
        condition_900_15 = [(900, 15)] * count_900_15  # 转速900rpm, 压力15MPa
        condition_900_18 = [(900, 18)] * count_900_18  # 转速900rpm, 压力18MPa

        # 合并所有工况标签
        self.condition_data = condition_1500_9 + condition_1500_12 + condition_1500_15 + condition_1500_18 + \
                              condition_1200_9 + condition_1200_12 + condition_1200_15 + condition_1200_18 + \
                              condition_900_9 + condition_900_12 + condition_900_15 + condition_900_18

        self.len = self.y_data.shape[0]  # 获得样本数量

    def __getitem__(self, item):
        rpm, pressure = self.condition_data[item]
        return self.x_data[item], self.y_data[item], rpm, pressure  # 返回数据、标签、转速、压力

    def __len__(self):
        return self.len


# 定义验证集
class Looseslipper_ValDataset(Dataset):
    def __init__(self):
        val_normal = scio.loadmat('..\\dataset\\detection_normal_900_12_val_2048.mat')
        normal = val_normal['detection_normal_900_12_val_2048']

        self.normal_data = normal[0, 0:409600]  # 样本数：200

        self.normal_data = torch.from_numpy(self.normal_data)

        self.normal_data = self.normal_data.view(-1, 1, 1, 2048).to(torch.float32)

        val_loose8067 = scio.loadmat('..\\dataset\\loose6333_12conditions\\detection_loose6333_900_12_val_2048.mat')
        loose8067 = val_loose8067['detection_loose6333_900_12_val_2048']

        self.loose8067 = loose8067[0, 0:409600]  # 样本数：200

        self.loose8067 = torch.from_numpy(self.loose8067)

        self.loose8067 = self.loose8067.view(-1, 1, 1, 2048).to(torch.float32)

        self.x_data = [self.normal_data, self.loose8067]
        self.x_data = torch.cat(self.x_data, dim=0)

        size_normal = int(self.normal_data.shape[0])  # 计算标签数量
        size_loose = int(self.loose8067.shape[0])
        y_data1 = 0 * np.ones(size_normal)  # 正常数据标签，0
        y_data2 = 1 * np.ones(size_loose)  # 异常数据标签，1
        y = np.append(y_data1, y_data2)

        self.y_data = torch.from_numpy(y)  # 标签转为张量

        # 创建工况标签 (转速, 压力)
        # 所有验证数据都是1500rpm, 15MPa工况
        condition_normal = [(900, 12)] * size_normal  # 正常数据工况标签
        condition_loose = [(900, 12)] * size_loose  # 异常数据工况标签

        # 合并所有工况标签
        self.condition_data = condition_normal + condition_loose

        self.len = self.y_data.shape[0]  # 获得样本数量

    def __getitem__(self, item):
        rpm, pressure = self.condition_data[item]
        return self.x_data[item], self.y_data[item], rpm, pressure  # 返回数据、标签、转速、压力

    def __len__(self):
        return self.len


# 定义测试集
class Looseslipper_TestDataset(Dataset):
    def __init__(self):
        test_normal = scio.loadmat('..\\dataset\\detection_normal_900_12_test_2048.mat')
        normal = test_normal['detection_normal_900_12_test_2048']

        self.normal_data = normal[0, 0:409600]  # 样本数：200

        self.normal_data = torch.from_numpy(self.normal_data)

        self.normal_data = self.normal_data.view(-1, 1, 1, 2048).to(torch.float32)

        test_loose8067 = scio.loadmat('..\\dataset\\loose6333_12conditions\\detection_loose6333_900_12_test_2048.mat')
        loose8067 = test_loose8067['detection_loose6333_900_12_test_2048']

        self.loose8067 = loose8067[0, 0:409600]  # 样本数：200

        self.loose8067 = torch.from_numpy(self.loose8067)

        self.loose8067 = self.loose8067.view(-1, 1, 1, 2048).to(torch.float32)

        self.x_data = [self.normal_data, self.loose8067]
        self.x_data = torch.cat(self.x_data, dim=0)

        size_normal = int(self.normal_data.shape[0])  # 计算标签数量
        size_loose = int(self.loose8067.shape[0])
        y_data1 = 0 * np.ones(size_normal)  # 正常数据标签，0
        y_data2 = 1 * np.ones(size_loose)  # 异常数据标签，1
        y = np.append(y_data1, y_data2)

        self.y_data = torch.from_numpy(y)  # 标签转为张量

        # 创建工况标签 (转速, 压力)
        # 所有测试数据都是1500rpm, 15MPa工况
        condition_normal = [(900, 12)] * size_normal  # 正常数据工况标签
        condition_loose = [(900, 12)] * size_loose  # 异常数据工况标签

        # 合并所有工况标签
        self.condition_data = condition_normal + condition_loose

        self.len = self.y_data.shape[0]  # 获得样本数量

    def __getitem__(self, item):
        rpm, pressure = self.condition_data[item]
        return self.x_data[item], self.y_data[item], rpm, pressure  # 返回数据、标签、转速、压力

    def __len__(self):
        return self.len


# 实例化对象
batch_size = 32

# 训练集
train_dataset = Looseslipper_TrainDataset()
train_loader = DataLoader(dataset=train_dataset,
                          batch_size=batch_size,
                          shuffle=True)

# 验证集
val_dataset = Looseslipper_ValDataset()
val_loader = DataLoader(dataset=val_dataset,
                        batch_size=batch_size,
                        shuffle=False,
                        drop_last=False)

# 测试集
test_dataset = Looseslipper_TestDataset()
test_loader = DataLoader(dataset=test_dataset,
                         batch_size=batch_size,
                         shuffle=False,
                         drop_last=False)

# VAE模型
latent_dim = 64  # 隐变量维度
input_dim = 1 * 2048  # 输入层维度
inter_dim = 1024  # 过渡层维度


# 增加center变量（可学习参数）
# center = torch.zeros(latent_dim, device=device, requires_grad=True)

# ===== 步骤1: 动态创建工况特定的center字典 =====
def extract_known_conditions_from_dataset(train_dataset):
    """从训练数据集中提取所有唯一的工况"""
    known_conditions = set()
    for i in range(len(train_dataset)):
        _, _, rpm, pressure = train_dataset[i]
        # 确保格式与现有代码一致：使用float格式
        condition_key = f"({float(rpm)}, {float(pressure)})"
        known_conditions.add(condition_key)
    return list(known_conditions)


# 遍历整个训练集，将所有出现过的 (转速, 压力) 组合提取出来
KNOWN_CONDITIONS = extract_known_conditions_from_dataset(train_dataset)
print(f"Found {len(KNOWN_CONDITIONS)} unique conditions: {KNOWN_CONDITIONS}")

# 为每个已知工况构造一个 learnable 的 center
condition_centers = {}
for condition_key in KNOWN_CONDITIONS:
    condition_centers[condition_key] = torch.zeros(latent_dim, device=device, requires_grad=True)

# 将所有center参数收集到列表中，便于加入优化器
all_centers = list(condition_centers.values())

# ===== 工况感知重构误差方差统计 =====
# 为每个已知工况构建重构误差统计信息
condition_recon_stats = {}
for condition_key in KNOWN_CONDITIONS:
    condition_recon_stats[condition_key] = {
        'running_mean': 0.0,  # 运行均值
        'running_var': 0.0,  # 运行方差（累积平方差）
        'count': 0  # 样本计数
    }

print(f"Initialized reconstruction error tracking for {len(condition_recon_stats)} conditions")


def update_condition_recon_stats(recon_errors, rpm_batch, pressure_batch, condition_recon_stats):
    """
    在线更新各工况的重构误差统计信息
    使用Welford算法进行数值稳定的在线均值和方差计算
    """
    for error, rpm, pressure in zip(recon_errors, rpm_batch, pressure_batch):
        condition_key = get_condition_key(rpm.item(), pressure.item())

        # 如果是未知工况，使用最近邻
        if condition_key not in condition_recon_stats:
            nearest_condition, _ = find_nearest_condition(
                rpm.item(), pressure.item(), list(condition_recon_stats.keys()))
            if nearest_condition:
                condition_key = nearest_condition

        if condition_key in condition_recon_stats:
            stats = condition_recon_stats[condition_key]
            error_val = error.item()

            # Welford在线算法更新均值和方差
            stats['count'] += 1
            delta = error_val - stats['running_mean']
            stats['running_mean'] += delta / stats['count']
            delta2 = error_val - stats['running_mean']
            stats['running_var'] += delta * delta2


def compute_condition_aware_variance_loss(recon_errors, rpm_batch, pressure_batch, condition_recon_stats):
    """
    计算工况感知的方差正则化损失
    目标：鼓励同一工况内的重构误差方差较小
    """
    # 按工况分组计算当前批次的方差
    condition_groups = {}
    for i, (error, rpm, pressure) in enumerate(zip(recon_errors, rpm_batch, pressure_batch)):
        condition_key = get_condition_key(rpm.item(), pressure.item())

        # 如果是未知工况，使用最近邻
        if condition_key not in condition_recon_stats:
            nearest_condition, _ = find_nearest_condition(
                rpm.item(), pressure.item(), list(condition_recon_stats.keys()))
            if nearest_condition:
                condition_key = nearest_condition

        if condition_key not in condition_groups:
            condition_groups[condition_key] = []
        condition_groups[condition_key].append(error)

    # 计算每个工况内的方差
    total_var_loss = 0.0
    valid_conditions = 0

    for condition_key, errors in condition_groups.items():
        if len(errors) > 1:  # 至少需要2个样本才能计算方差
            errors_tensor = torch.stack(errors)
            condition_var = torch.var(errors_tensor, unbiased=False)  # 使用样本方差
            total_var_loss += condition_var
            valid_conditions += 1

    if valid_conditions > 0:
        return total_var_loss / valid_conditions
    else:
        return torch.tensor(0.0, device=recon_errors[0].device, requires_grad=True)


def print_condition_recon_stats(condition_recon_stats, epoch):
    """打印各工况的重构误差统计信息"""
    print(f"\n=== Epoch {epoch} Condition Reconstruction Error Stats ===")
    for condition_key, stats in condition_recon_stats.items():
        if stats['count'] > 1:
            current_var = stats['running_var'] / (stats['count'] - 1)  # 样本方差
            current_std = math.sqrt(current_var)
            print(f"Condition {condition_key}: mean={stats['running_mean']:.6f}, "
                  f"std={current_std:.6f}, var={current_var:.6f}, count={stats['count']}")
        elif stats['count'] == 1:
            print(f"Condition {condition_key}: mean={stats['running_mean']:.6f}, "
                  f"std=N/A (single sample), count={stats['count']}")
        else:
            print(f"Condition {condition_key}: No samples yet")
    print("=" * 60)


def save_condition_recon_stats(condition_recon_stats, filename):
    """保存工况重构误差统计信息到JSON文件"""
    stats_dict = {}
    for condition_key, stats in condition_recon_stats.items():
        if stats['count'] > 1:
            current_var = stats['running_var'] / (stats['count'] - 1)
            current_std = math.sqrt(current_var)
        elif stats['count'] == 1:
            current_var = 0.0
            current_std = 0.0
        else:
            current_var = 0.0
            current_std = 0.0

        stats_dict[condition_key] = {
            'mean': stats['running_mean'],
            'variance': current_var,
            'std': current_std,
            'count': stats['count']
        }

    with open(filename, 'w') as f:
        json.dump(stats_dict, f, indent=2)
    print(f"Saved condition reconstruction stats to {filename}")


def linear_warmup_scheduler(epoch, total_epochs, max_lambda_var=0.05, warmup_epochs=3):
    """
    Linear warm-up调度器for方差损失权重

    Args:
        epoch: 当前epoch (0-based)
        total_epochs: 总训练轮数
        max_lambda_var: 最大方差损失权重
        warmup_epochs: warm-up的轮数

    Returns:
        当前epoch应使用的lambda_var值
    """
    if epoch < warmup_epochs:
        # 线性增长阶段：从0增长到max_lambda_var
        return max_lambda_var * (epoch / warmup_epochs)
    else:
        # 稳定阶段：保持最大值
        return max_lambda_var


def save_training_schedule_info(lambda_var_history, filename):
    """保存训练调度信息到文件"""
    schedule_info = {
        'lambda_var_history': lambda_var_history,
        'scheduler_type': 'linear_warmup',
        'max_lambda_var': 0.05,
        'warmup_epochs': 3,
        'total_epochs': len(lambda_var_history)
    }

    with open(filename, 'w') as f:
        json.dump(schedule_info, f, indent=2)
    print(f"Saved training schedule info to {filename}")


# ===== 使用真实数据初始化center =====
def initialize_centers_with_data(train_loader, vae, condition_centers):
    """使用训练数据的均值初始化各工况的center"""
    print("Initializing condition centers with actual data means...")
    vae.eval()
    condition_samples = {key: [] for key in condition_centers.keys()}

    with torch.no_grad():
        for data in train_loader:
            inputs, labels, rpm, pressure = data
            inputs = inputs.to(device)

            # VAE编码获取隐变量
            mu, _ = vae.encode(inputs)

            # 按工况分组收集隐变量的 mu
            for i, (r, p, mu_sample) in enumerate(zip(rpm, pressure, mu)):
                condition_key = get_condition_key(r.item(), p.item())
                if condition_key in condition_samples:
                    condition_samples[condition_key].append(mu_sample)  # 把样本在隐空间中的 mu 向量添加到对应工况的列表中

    # 计算每个工况的均值并初始化center
    for condition_key, samples in condition_samples.items():
        if samples:
            mean_mu = torch.stack(samples).mean(dim=0)
            condition_centers[condition_key].data.copy_(mean_mu)
            print(f"  Initialized center for {condition_key} with {len(samples)} samples")
        else:
            print(f"  Warning: No samples found for condition {condition_key}")

    vae.train()


# 在训练开始前初始化center（注释掉以使用零初始化）
# initialize_centers_with_data(train_loader, vae, condition_centers)

# ===== 步骤2: 添加最近邻工况查找和center获取函数 =====
# 解析工况字符串为数值：在查找最近邻工况时，需要从已有 condition_key 字符串中提取其实际数值进行距离计算
def parse_condition_key(condition_key):
    """
    解析工况键，提取rpm和pressure数值
    例如: "(1500.0, 9.0)" -> (1500.0, 9.0)
    """
    try:
        # 去除括号并分割
        condition_clean = condition_key.strip("()")
        rpm_str, pressure_str = condition_clean.split(", ")
        return float(rpm_str), float(pressure_str)
    except Exception as e:
        print(f"Error parsing condition key {condition_key}: {e}")
        return None, None


def find_nearest_condition(target_rpm, target_pressure, known_conditions):
    """
    找到最接近目标工况的已知工况
    Args:
        target_rpm: 目标转速
        target_pressure: 目标压力
        known_conditions: 已知工况键列表
    Returns:
        nearest_condition_key: 最近的工况键
        min_distance: 最小距离
    """
    if not known_conditions:
        return None, float('inf')

    min_distance = float('inf')
    nearest_condition = None

    # 归一化参数（基于训练数据的范围）
    rpm_range = 2500 - 600  # 假设rpm范围[600, 2500]
    pressure_range = 30 - 0  # 假设压力范围[0, 30]

    for condition_key in known_conditions:
        known_rpm, known_pressure = parse_condition_key(condition_key)
        if known_rpm is None or known_pressure is None:
            continue

        # 计算归一化的欧氏距离
        norm_rpm_diff = (target_rpm - known_rpm) / rpm_range
        norm_pressure_diff = (target_pressure - known_pressure) / pressure_range
        distance = (norm_rpm_diff ** 2 + norm_pressure_diff ** 2) ** 0.5

        if distance < min_distance:
            min_distance = distance
            nearest_condition = condition_key

    return nearest_condition, min_distance


def get_condition_key(rpm, pressure):
    """
    根据rpm和pressure生成工况键（与现有代码保持一致）
    确保格式统一：始终使用float格式
    """
    return f"({float(rpm)}, {float(pressure)})"


def get_condition_centers_for_batch(rpm_batch, pressure_batch, condition_centers):
    """
    为batch中的每个样本获取对应的center（使用最近邻策略）
    Args:
        rpm_batch: 转速batch
        pressure_batch: 压力batch
        condition_centers: 工况特定center字典
    Returns:
        batch_centers: [batch_size, latent_dim]
        condition_keys: 该batch对应的工况键列表
        used_centers: 实际使用的center键列表（可能是最近邻）
    """
    batch_centers = []
    condition_keys = []
    used_centers = []
    known_conditions = list(condition_centers.keys())

    for rpm, pressure in zip(rpm_batch, pressure_batch):
        target_rpm = rpm.item()
        target_pressure = pressure.item()
        condition_key = get_condition_key(target_rpm, target_pressure)
        condition_keys.append(condition_key)

        if condition_key in condition_centers:
            # 直接匹配
            batch_centers.append(condition_centers[condition_key])
            used_centers.append(condition_key)
        else:
            # 使用最近邻策略
            nearest_condition, distance = find_nearest_condition(
                target_rpm, target_pressure, known_conditions)

            if nearest_condition is not None:
                batch_centers.append(condition_centers[nearest_condition])
                used_centers.append(nearest_condition)
                if distance > 0:  # 只在真正是未知工况时打印
                    print(
                        f"Unknown condition {condition_key}, using nearest: {nearest_condition} (distance: {distance:.4f})")
            else:
                # 极端情况：没有任何已知工况，使用第一个center
                first_condition = known_conditions[0] if known_conditions else None
                if first_condition:
                    batch_centers.append(condition_centers[first_condition])
                    used_centers.append(first_condition)
                    print(
                        f"No valid nearest condition found for {condition_key}, using first available: {first_condition}")
                else:
                    raise ValueError("No known conditions available in condition_centers")

    return torch.stack(batch_centers), condition_keys, used_centers


# ===== 步骤3: 修改center loss计算函数 =====
# 让每个样本的隐变量 mu 更接近其对应工况（或最近邻工况）在隐空间的中心向量 center。
def compute_condition_aware_center_loss(mu, rpm_batch, pressure_batch, condition_centers):
    """
    计算工况感知的center loss（使用最近邻策略）
    """
    # 获取batch中每个样本对应的center
    batch_centers, condition_keys, used_centers = get_condition_centers_for_batch(
        rpm_batch, pressure_batch, condition_centers)

    # 计算到对应center的距离
    center_loss = ((mu - batch_centers) ** 2).mean()

    return center_loss, condition_keys, used_centers


class VAE(nn.Module):
    def __init__(self, input_dim=input_dim, inter_dim=inter_dim, latent_dim=latent_dim):
        super(VAE, self).__init__()

        # 编码器
        self.fc1 = nn.Linear(input_dim, inter_dim)

        self.fc21 = nn.Linear(inter_dim, latent_dim)  # 均值
        self.fc22 = nn.Linear(inter_dim, latent_dim)  # 方差

        self.bn_mu = nn.BatchNorm1d(latent_dim)  # BatchNorm VAE, 解决后验坍塌
        self.bn_logvar = nn.BatchNorm1d(latent_dim)

        self.dropout = nn.Dropout(0.2)

        # 解码器
        self.fc3 = nn.Linear(latent_dim, inter_dim)
        self.fc4 = nn.Linear(inter_dim, input_dim)
        self.sigmoid = nn.Sigmoid()

    def encode(self, x):
        x = x.view(x.size(0), -1)  # 将x展平为二维向量，便于处理
        h1 = F.relu(self.fc1(x))
        h1 = self.dropout(h1)

        mu = self.bn_mu(self.fc21(h1))  # BatchNorm VAE, 解决后验坍塌
        logvar = self.bn_logvar(self.fc22(h1))
        return mu, logvar

    # VAE重参数化技巧
    def reparameterize(self, mu, logvar):
        std = torch.exp(0.5 * logvar)
        eps = torch.randn_like(std)
        return mu + eps * std

    def decode(self, z):
        h3 = F.relu(self.fc3(z))
        h3 = self.dropout(h3)
        return self.sigmoid(self.fc4(h3))

    def forward(self, x):
        org_size = x.size()  # 获取输入x的原始尺寸
        batch = org_size[0]  # 获取批次大小
        x = x.view(batch, -1)  # 将x展平为二维向量，便于处理

        mu, logvar = self.encode(x)
        z = self.reparameterize(mu, logvar)  # 对隐变量z进行重参数化采样

        recon_x = self.decode(z).view(org_size)  # 解码器重构输入，并恢复原始尺寸

        return recon_x, mu, logvar  # 返回重构后的输入、均值和对数方差


# VAE损失由重构损失和KL损失组成
def vae_loss(recon_x, x, mu, logvar):
    recon_loss = F.mse_loss(recon_x, x, reduction='sum') / x.size(0)
    kl_loss = -0.5 * torch.sum(1 + logvar - mu.pow(2) - logvar.exp())
    return recon_loss + kl_loss, recon_loss, kl_loss


# 训练 VAE 模型
vae = VAE(input_dim, inter_dim, latent_dim).to(device)
# 将center加入优化器
vae_optimizer = optim.Adam(list(vae.parameters()) + all_centers, lr=0.001)  # 设置L2正则化参数
vae_epochs = 2

# 初始化记录损失的列表
all_losses = {
    'total_loss': [],
    'recon_loss': [],
    'kl_loss': []
}

# 初始化lambda_var调度历史记录
lambda_var_history = []

for vae_epoch in range(vae_epochs):
    # 使用Linear Warm-up Scheduler计算当前epoch的lambda_var
    current_lambda_var = linear_warmup_scheduler(
        epoch=vae_epoch,
        total_epochs=vae_epochs,
        max_lambda_var=0.05,
        warmup_epochs=3
    )
    lambda_var_history.append(current_lambda_var)

    print(f"VAE Epoch {vae_epoch} - Using lambda_var = {current_lambda_var:.4f}")
    # 将模型设置为训练模式
    vae.train()
    vae_train_loss = 0.0
    epoch_losses = {
        'total_loss': [],
        'recon_loss': [],
        'kl_loss': []
    }

    for idx, data in enumerate(train_loader, 0):
        # prepare data
        inputs, labels, rpm, pressure = data
        inputs = inputs.to(device)

        # forward
        recon_x, mu, logvar = vae(inputs)

        # 计算损失
        loss, recon_loss, kl_loss = vae_loss(recon_x, inputs, mu, logvar)

        # 工况感知 center loss
        center_loss, condition_keys, used_centers = compute_condition_aware_center_loss(mu, rpm, pressure,
                                                                                        condition_centers)
        lambda_center = 0.5  # 可调超参数

        # 计算逐样本重构误差（用于工况感知方差损失）
        batch_recon_errors = F.mse_loss(recon_x, inputs, reduction='none')
        batch_recon_errors = batch_recon_errors.view(batch_recon_errors.size(0), -1).mean(dim=1)

        # 计算工况感知的重构误差方差损失
        condition_var_loss = compute_condition_aware_variance_loss(
            batch_recon_errors, rpm, pressure, condition_recon_stats)

        # 更新工况重构误差统计信息
        update_condition_recon_stats(batch_recon_errors, rpm, pressure, condition_recon_stats)

        # 使用动态的方差损失权重（通过Linear Warm-up Scheduler计算）
        total_loss = loss + lambda_center * center_loss + current_lambda_var * condition_var_loss
        vae_train_loss += total_loss.item()

        # 记录损失
        epoch_losses['total_loss'].append(total_loss.item())
        epoch_losses['recon_loss'].append(recon_loss.item())
        epoch_losses['kl_loss'].append(kl_loss.item())

        # backward
        vae_optimizer.zero_grad()
        total_loss.backward()

        # update
        vae_optimizer.step()

        # 每 32 个批次打印当前总损失、当前批次索引
        if idx % 32 == 0:
            print(
                f"Training loss {loss: .3f}, Center loss {center_loss:.3f}, Condition Var loss {condition_var_loss:.6f} (λ_var={current_lambda_var:.4f}), "
                f"Recon loss {recon_loss: .3f}, KL loss {kl_loss: .3f} in Step {idx}")
            print(f"  Original conditions: {dict(Counter(condition_keys))}")
            print(f"  Used centers: {dict(Counter(used_centers))}")

            # 统计最近邻使用情况
            exact_matches = sum(1 for orig, used in zip(condition_keys, used_centers) if orig == used)
            nearest_neighbor_uses = len(condition_keys) - exact_matches
            if nearest_neighbor_uses > 0:
                print(f"  Nearest neighbor usage: {nearest_neighbor_uses}/{len(condition_keys)} samples")

        # training curve
        global_iter_num_train = vae_epoch * len(train_loader) + idx + 1
        VAE_train_wind.line([total_loss.item()], [global_iter_num_train], win='VAE_train', update='append')
        VAE_recon_loss.line([recon_loss.item()], [global_iter_num_train], win='VAE_recon_loss', update='append')
        VAE_KL.line([kl_loss.item()], [global_iter_num_train], win='VAE_KL', update='append')

    # 计算并保存每个epoch的平均损失
    for key in epoch_losses:
        epoch_avg = np.mean(epoch_losses[key])
        all_losses[key].append(epoch_avg)
        print(f"Epoch {vae_epoch} average {key}: {epoch_avg:.6f}")

    # 打印工况重构误差统计信息
    print_condition_recon_stats(condition_recon_stats, vae_epoch)

    # 保存工况重构误差统计信息
    save_condition_recon_stats(condition_recon_stats, f'condition_recon_stats_epoch_{vae_epoch}.json')

    # 保存损失记录到文件
    loss_df = pd.DataFrame(all_losses)
    loss_df.to_csv('vae_training_losses.csv', index=False)

    # 获取未加噪的隐变量
    vae.eval()
    latent_for_diffusion = []
    condition_for_diffusion = []
    with torch.no_grad():
        for data in train_loader:
            inputs, labels, rpm, pressure = data
            inputs = inputs.to(device)

            # 提取均值 mu 作为未加噪隐变量
            mu, _ = vae.encode(inputs)
            latent_for_diffusion.append(mu.cpu())  # 将隐变量存储到列表中
            # 保存对应的工况信息
            condition_for_diffusion.extend([(r.item(), p.item()) for r, p in zip(rpm, pressure)])

    # 拼接所有批次的隐变量
    latent_for_diffusion = torch.cat(latent_for_diffusion, dim=0)
    torch.save(latent_for_diffusion, "latent_for_diffusion.pt")
    # 保存工况信息
    torch.save(condition_for_diffusion, "condition_for_diffusion.pt")

# 保存最终的工况重构误差统计信息
print("\n=== VAE Training Completed ===")
print("Final condition-aware reconstruction error statistics:")
print_condition_recon_stats(condition_recon_stats, "Final")
save_condition_recon_stats(condition_recon_stats, 'final_condition_recon_stats.json')
print("Saved final condition reconstruction statistics to 'final_condition_recon_stats.json'")

# 保存lambda_var调度历史信息
save_training_schedule_info(lambda_var_history, 'lambda_var_schedule_history.json')

# 打印调度器效果总结
print("\n=== Lambda_var Scheduling Summary ===")
for epoch, lambda_var in enumerate(lambda_var_history):
    print(f"Epoch {epoch}: λ_var = {lambda_var:.4f}")
print(f"Scheduler Type: Linear Warm-up")
print(f"Max λ_var: {max(lambda_var_history):.4f}")
print(f"Warm-up Epochs: 3")
print("=" * 50)


# Latent Diffusion Model 扩散模型
# 将工况（转速、压力）作为条件嵌入
# 转速范围 [600, 2500], 压力范围 [0, 30]
class ConditionEmbedding(nn.Module):
    def __init__(self, embed_dim=320):
        super().__init__()
        self.embed_dim = embed_dim

        # 注册归一化参数
        self.register_buffer('rpm_min', torch.tensor(600.0))
        self.register_buffer('rpm_range', torch.tensor(2500.0 - 600.0))  # 1900
        self.register_buffer('pressure_min', torch.tensor(0.0))
        self.register_buffer('pressure_range', torch.tensor(30.0 - 0.0))  # 30

        # 独立维度分配（每个条件使用完整维度）
        dim_per_cond = embed_dim  # 每个条件使用 sin+cos 总共 embed_dim 维度
        freqs_dim = dim_per_cond // 2  # 正弦和余弦各占一半

        # 为rpm生成适合1500±1000范围的频率基
        self.register_buffer('freqs_rpm', torch.exp(
            torch.linspace(
                start=math.log(1.0),
                end=math.log(1000.0),  # 适合rpm范围
                steps=freqs_dim
            )
        ))

        # 为pressure生成适合15±15范围的频率基
        self.register_buffer('freqs_pressure', torch.exp(
            torch.linspace(
                start=math.log(0.1),  # 更低的起始频率
                end=math.log(100.0),  # 适合MPa范围
                steps=freqs_dim
            )
        ))

        # 增强版融合网络
        self.fusion = nn.Sequential(
            nn.Linear(embed_dim * 2, embed_dim * 4),  # 输入为 rpm 和 pressure 拼接
            nn.SiLU(),
            nn.Dropout(0.1),
            nn.Linear(embed_dim * 4, embed_dim * 4),
            nn.LayerNorm(embed_dim * 4)
        )

    def forward(self, rpm, pressure):
        # 自动类型转换和设备转移
        if isinstance(rpm, (int, float)):
            rpm = torch.tensor([rpm], dtype=torch.float32, device=next(self.parameters()).device)
        elif isinstance(rpm, (list, tuple)):
            rpm = torch.tensor(rpm, dtype=torch.float32, device=next(self.parameters()).device)
        else:
            rpm = rpm.to(dtype=torch.float32, device=next(self.parameters()).device)

        if isinstance(pressure, (int, float)):
            pressure = torch.tensor([pressure], dtype=torch.float32, device=next(self.parameters()).device)
        elif isinstance(pressure, (list, tuple)):
            pressure = torch.tensor(pressure, dtype=torch.float32, device=next(self.parameters()).device)
        else:
            pressure = pressure.to(dtype=torch.float32, device=next(self.parameters()).device)

        # 确保rpm和pressure是一维张量
        if rpm.dim() == 0:
            rpm = rpm.unsqueeze(0)
        if pressure.dim() == 0:
            pressure = pressure.unsqueeze(0)

        # 归一化到[0,1]范围
        rpm_norm = (rpm - self.rpm_min) / self.rpm_range
        pressure_norm = (pressure - self.pressure_min) / self.pressure_range

        # 嵌入 RPM 和压力
        rpm_embed = self.freqs_rpm.unsqueeze(0) * rpm_norm.unsqueeze(-1)
        pressure_embed = self.freqs_pressure.unsqueeze(0) * pressure_norm.unsqueeze(-1)

        # 正弦余弦嵌入
        rpm_embed = torch.cat([torch.sin(rpm_embed), torch.cos(rpm_embed)], dim=-1)
        pressure_embed = torch.cat([torch.sin(pressure_embed), torch.cos(pressure_embed)], dim=-1)

        # 拼接并融合（现在每个条件已经包含sin+cos，所以直接拼接）
        cond = torch.cat([rpm_embed, pressure_embed], dim=-1)
        return self.fusion(cond)


# 生成时间步长的时间嵌入向量 （位置编码公式）
def get_time_embedding(timestep):
    # (160, )
    freqs = torch.pow(10000, -torch.arange(start=0, end=160, dtype=torch.float32) / 160)  # 生成频率向量 freqs, 10000^(-i/160)
    # (1, 160)
    x = torch.tensor([timestep], dtype=torch.float32)[:, None] * freqs[None]  # 每个元素是 timestep 乘以对应频率的结果
    # (1, 320)
    return torch.cat([torch.cos(x), torch.sin(x)], dim=-1)  # 生成最终的时间嵌入向量


# 增加时间嵌入的表达能力
class TimeEmbedding(nn.Module):
    def __init__(self, n_embed: int):
        super().__init__()
        self.linear_1 = nn.Linear(n_embed, 4 * n_embed)
        self.linear_2 = nn.Linear(4 * n_embed, 4 * n_embed)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        # x: (1,320)
        x = self.linear_1(x)
        x = F.silu(x)
        x = self.linear_2(x)
        # x: (1,1280)
        return x


# 将隐变量与时间变量关联起来（噪声 & 时间）
class UNET_ResidualBlock(nn.Module):
    def __init__(self, in_channels: int, out_channels: int, n_time=1280):
        super().__init__()
        self.groupnorm_feature = nn.GroupNorm(32, in_channels)
        self.conv_feature = nn.Conv2d(in_channels, out_channels, kernel_size=(1, 3), padding=(0, 1))
        self.linear_time = nn.Linear(n_time, out_channels)

        self.groupnorm_merged = nn.GroupNorm(32, out_channels)
        self.conv_merged = nn.Conv2d(out_channels, out_channels, kernel_size=(1, 3), padding=(0, 1))

        if in_channels == out_channels:
            self.residual_layer = nn.Identity()
        else:
            self.residual_layer = nn.Conv2d(in_channels, out_channels, kernel_size=(1, 1), padding=(0, 0))

    def forward(self, feature, time):
        # feature: [Batch_size, In_Channels, Height, Width]
        # time: (Batch_size, 1280)
        residue = feature

        feature = self.groupnorm_feature(feature)
        feature = F.silu(feature)
        feature = self.conv_feature(feature)

        time = F.silu(time)
        time = self.linear_time(time)

        # time: (Batch_size, Out_Channels, 1, 1)
        merged = feature + time.unsqueeze(-1).unsqueeze(-1)  # 将时间变量扩展到与特征图相同的形状，并与特征图相加
        merged = self.groupnorm_merged(merged)
        merged = F.silu(merged)
        merged = self.conv_merged(merged)

        return merged + self.residual_layer(residue)


# UNet_Attention 中的 SelfAttention (在源码基础上的修改版)
# d_embed 相当于通道数（一个文字用d_embed维度向量表示，图片一个像素用d_embed=3个通道信息表示，振动信号单位时间状态用d_embed个通道振动数值表示）
class SelfAttention(nn.Module):
    def __init__(self, n_heads: int, d_embed: int, in_proj_bias=True, out_proj_bias=True):
        super().__init__()
        self.in_proj = nn.Linear(d_embed, 3 * d_embed, bias=in_proj_bias)
        self.out_proj = nn.Linear(d_embed, d_embed, bias=out_proj_bias)
        self.norm = nn.LayerNorm(d_embed)
        self.n_heads = n_heads
        self.d_head = d_embed // n_heads

    def forward(self, x: torch.Tensor, causal_mask=False):
        # x: (Batch_Size, Seq_Len, Dim)
        input_shape = x.shape
        batch_size, sequence_length, d_embed = input_shape

        residual = x
        x = self.norm(x)  # Add Norm

        intermim_shape = (batch_size, sequence_length, self.n_heads, self.d_head)  # (Batch_Size, Seq_Len, H, Dim / H)

        # (Batch_Size, Seq_Len, Dim) -> (Batch_Size, Seq_Len, Dim * 3) -> 3 tensors of shape (Batch_Size, Seq_Len, Dim)
        q, k, v = self.in_proj(x).chunk(3, dim=-1)
        # (Batch_Size, Seq_Len, Dim) -> (Batch_Size, Seq_Len, H, Dim / H) -> (Batch_Size, H, Seq_Len, Dim / H)
        q = q.view(intermim_shape).transpose(1, 2)
        k = k.view(intermim_shape).transpose(1, 2)
        v = v.view(intermim_shape).transpose(1, 2)

        # (Batch_Size, H, Seq_Len, Seq_Len)
        weight = q @ k.transpose(-1, -2)

        if causal_mask:
            mask = torch.ones_like(weight, dtype=torch.bool).triu(1)
            weight.masked_fill_(mask, -torch.inf)

        weight /= math.sqrt(self.d_head)
        weight = F.softmax(weight, dim=-1)

        # (Batch_Size, H, Seq_Len, Seq_Len) -> (Batch_Size, H, Seq_Len, Dim / H)
        output = weight @ v

        # (Batch_Size, H, Seq_Len, Dim / H) -> (Batch_Size, Seq_Len, H, Dim / H)
        output = output.transpose(1, 2)
        # (Batch_Size, Seq_Len, Dim)
        output = output.reshape(input_shape)

        output = self.out_proj(output)

        # (Batch_Size, Seq_Len, Dim)
        return output + residual  # Add residual


# UNet_Attention 中的 CrossAttention (在源码基础上的修改版)
# n_heads 注意力头的数量, d_embed 嵌入维度, d_cross 上下文输入的维度
class CrossAttention(nn.Module):
    def __init__(self, n_heads: int, d_embed: int, d_cross: int, in_proj_bias=True, out_proj_bias=True):
        super().__init__()
        self.q_proj = nn.Linear(d_embed, d_embed, bias=in_proj_bias)
        self.k_proj = nn.Linear(d_cross, d_embed, bias=in_proj_bias)
        self.v_proj = nn.Linear(d_cross, d_embed, bias=in_proj_bias)
        self.out_proj = nn.Linear(d_embed, d_embed, bias=out_proj_bias)

        self.n_head = n_heads
        self.d_head = d_embed // n_heads
        self.norm = nn.LayerNorm(d_embed)

    def forward(self, x, y):
        # x: (latent): (Batch_Size, Seq_Len_Q, Dim_Q)
        # y: (context): (Batch_Size, Seq_Len_KV, Dim_KV) = (Batch_Size, 1, 320)
        B, L_q, D = x.shape
        L_kv = y.shape[1]

        x_residual = x
        x = self.norm(x)  # Add Norm

        # Multiply query by Wq
        q = self.q_proj(x)
        # Multiply key & value by Wk & Wv
        k = self.k_proj(y)
        v = self.v_proj(y)

        # (Batch_Size, H, Seq_Len, Dim / H)
        q = q.view(B, L_q, self.n_head, self.d_head).transpose(1, 2)
        k = k.view(B, L_kv, self.n_head, self.d_head).transpose(1, 2)
        v = v.view(B, L_kv, self.n_head, self.d_head).transpose(1, 2)

        weight = q @ k.transpose(-1, -2)
        weight /= math.sqrt(self.d_head)
        weight = F.softmax(weight, dim=-1)
        output = weight @ v

        # (Batch_Size, Seq_Len, H, Dim / H)
        output = output.transpose(1, 2).contiguous().view(B, L_q, D)
        output = self.out_proj(output)

        return output + x_residual  # Add residual


# 将隐变量与 context (工况) 关联起来 (噪声 & 工况)
class UNET_AttentionBlock(nn.Module):
    def __init__(self, n_head: int, n_embed: int, d_context=1280):
        super().__init__()
        channels = n_head * n_embed  # n_head 注意力头的数量, n_embed 每个注意力头的嵌入维度

        self.groupnorm = nn.GroupNorm(32, channels, eps=1e-6)
        self.conv_input = nn.Conv2d(channels, channels, kernel_size=(1, 1), padding=(0, 0))

        self.layernorm_1 = nn.LayerNorm(channels)
        self.attention_1 = SelfAttention(n_head, channels, in_proj_bias=False)

        self.layernorm_2 = nn.LayerNorm(channels)
        self.attention_2 = CrossAttention(n_head, channels, d_context, in_proj_bias=False)

        self.layernorm_3 = nn.LayerNorm(channels)
        self.linear_geglu_1 = nn.Linear(channels, 4 * channels * 2)
        self.linear_geglu_2 = nn.Linear(4 * channels, channels)

        self.conv_output = nn.Conv2d(channels, channels, kernel_size=(1, 1), padding=(0, 0))

    def forward(self, x, context):
        # x: (Batch_size, Channels, Height, Width)
        # context: (Batch, 1280)
        residue_long = x

        x = self.groupnorm(x)
        x = self.conv_input(x)

        n, c, h, w = x.shape
        x = x.view((n, c, h * w))
        x = x.transpose(-1, -2)  # (B, HW, C)

        # Normalization + Self Attention with skip connection
        residue_short = x
        x = self.layernorm_1(x)
        self.attention_1(x)
        x += residue_short

        # Normalization + Cross Attention with skip connection
        residue_short = x
        x = self.layernorm_2(x)
        # Cross Attention
        context = context.unsqueeze(1)  # (B, 1, D_context)
        x = self.attention_2(x, context)  # 将隐变量与context相关联
        x += residue_short

        # Normalization + Feed Forward with GeGLU and skip connection
        residue_short = x
        x = self.layernorm_3(x)
        x, gate = self.linear_geglu_1(x).chunk(2, dim=-1)  # 将线性层的输出分成两部分，一部分用于计算激活值，另一部分用于门控（Gating）
        x = x * F.gelu(gate)  # 应用 GeGLU（Gated Linear Unit with GELU activation）激活函数
        x = self.linear_geglu_2(x)
        x += residue_short

        # x: (Batch_size, Channels, Height * Width)
        x = x.transpose(-1, -2)
        x = x.view((n, c, h, w))

        return self.conv_output(x) + residue_long


# 对输入特征图进行上采样
class Upsample(nn.Module):
    def __init__(self, channels: int):
        super().__init__()
        self.conv = nn.Conv2d(channels, channels, kernel_size=(1, 3), padding=(0, 1))

    def forward(self, x):
        # (Batch_size, Channels, Height, Width) -> (Batch_size, Channels, Height, Width * 2)
        x = F.interpolate(x, scale_factor=(1, 2), mode="nearest")  # 高度维度不变，宽度维度放大两倍
        return self.conv(x)


class SwitchSequential(nn.Sequential):
    def forward(self, x: torch.Tensor, context: torch.Tensor, time: torch.Tensor) -> torch.Tensor:
        for layer in self:
            if isinstance(layer, UNET_AttentionBlock):
                x = layer(x, context)
            elif isinstance(layer, UNET_ResidualBlock):
                x = layer(x, time)
            else:
                x = layer(x)
        return x


# 编码器：逐步减少空间分辨率，增加通道数，提取高层次的语义信息。
# 解码器：逐步恢复空间分辨率，减少通道数，同时通过跳跃连接从编码器获取低层次的细节信息
class UNET(nn.Module):
    def __init__(self):
        super().__init__()
        self.encoders = nn.ModuleList([
            SwitchSequential(nn.Conv2d(1, 320, kernel_size=(1, 3), padding=(0, 1))),  # 增加通道数
            SwitchSequential(UNET_ResidualBlock(320, 320), UNET_AttentionBlock(8, 40)),
            SwitchSequential(UNET_ResidualBlock(320, 320), UNET_AttentionBlock(8, 40)),
            SwitchSequential(nn.Conv2d(320, 320, kernel_size=(1, 3), stride=(1, 2), padding=(0, 1))),  # 减少空间分辨率
            SwitchSequential(UNET_ResidualBlock(320, 640), UNET_AttentionBlock(8, 80)),
            SwitchSequential(UNET_ResidualBlock(640, 640), UNET_AttentionBlock(8, 80)),
            SwitchSequential(nn.Conv2d(640, 640, kernel_size=(1, 3), stride=(1, 2), padding=(0, 1))),
            SwitchSequential(UNET_ResidualBlock(640, 1280), UNET_AttentionBlock(8, 160)),
            SwitchSequential(UNET_ResidualBlock(1280, 1280), UNET_AttentionBlock(8, 160)),
            SwitchSequential(nn.Conv2d(1280, 1280, kernel_size=(1, 3), stride=(1, 2), padding=(0, 1))),
            SwitchSequential(UNET_ResidualBlock(1280, 1280)),
            SwitchSequential(UNET_ResidualBlock(1280, 1280)),
        ])

        self.bottleneck = SwitchSequential(
            UNET_ResidualBlock(1280, 1280),
            UNET_AttentionBlock(8, 160),
            UNET_ResidualBlock(1280, 1280)
        )

        self.decoders = nn.ModuleList([
            SwitchSequential(UNET_ResidualBlock(2560, 1280)),  # 减少通道数
            SwitchSequential(UNET_ResidualBlock(2560, 1280)),
            SwitchSequential(UNET_ResidualBlock(2560, 1280), Upsample(1280)),  # 恢复空间分辨率
            SwitchSequential(UNET_ResidualBlock(2560, 1280), UNET_AttentionBlock(8, 160)),
            SwitchSequential(UNET_ResidualBlock(2560, 1280), UNET_AttentionBlock(8, 160)),
            SwitchSequential(UNET_ResidualBlock(1920, 1280), UNET_AttentionBlock(8, 160), Upsample(1280)),
            SwitchSequential(UNET_ResidualBlock(1920, 640), UNET_AttentionBlock(8, 80)),
            SwitchSequential(UNET_ResidualBlock(1280, 640), UNET_AttentionBlock(8, 80)),
            SwitchSequential(UNET_ResidualBlock(960, 640), UNET_AttentionBlock(8, 80), Upsample(640)),
            SwitchSequential(UNET_ResidualBlock(960, 320), UNET_AttentionBlock(8, 40)),
            SwitchSequential(UNET_ResidualBlock(640, 320), UNET_AttentionBlock(8, 40)),
            SwitchSequential(UNET_ResidualBlock(640, 320), UNET_AttentionBlock(8, 40)),
        ])

    def forward(self, x, time, context):
        # 编码
        skip_connections = []
        for encoder in self.encoders:
            x = encoder(x, context, time)
            skip_connections.append(x)  # 每次编码后，当前特征图被存储到skip_connections列表中

        # Bottleneck
        for layer in self.bottleneck:
            if isinstance(layer, UNET_AttentionBlock):
                x = layer(x, context)
            elif isinstance(layer, UNET_ResidualBlock):
                x = layer(x, time)
            else:
                x = layer(x)

        # 解码
        for decoder in self.decoders:
            x = torch.cat((x, skip_connections.pop()), dim=1)  # 从 skip_connections 中弹出对应的编码器输出，并通过 torch.cat 实现跳跃连接
            x = decoder(x, context, time)  # 解码器逐层处理特征图

        return x


class UNET_OutputLayer(nn.Module):
    def __init__(self, in_channels: int, out_channels: int):
        super().__init__()
        self.groupnorm = nn.GroupNorm(32, in_channels)
        self.conv = nn.Conv2d(in_channels, out_channels, kernel_size=(1, 3), padding=(0, 1))

    def forward(self, x):
        # (Batch_size, 320, Height / 8, Width / 8)
        x = self.groupnorm(x)
        x = F.silu(x)
        x = self.conv(x)
        # (Batch_size, 1, Height / 8, Width / 8)
        return x


# Diffusion 输出为预测的噪声
class Diffusion(nn.Module):
    def __init__(self):
        super().__init__()
        self.time_embedding = TimeEmbedding(320)
        self.condition_embedding = ConditionEmbedding(320)
        self.unet = UNET()
        self.final = UNET_OutputLayer(320, 1)

    def forward(self, latent: torch.Tensor, context: torch.Tensor, time: torch.Tensor):
        time = self.time_embedding(time)
        output = self.unet(latent, context, time)
        output = self.final(output)  # 输出为预测的噪声
        return output


# 一系列 beta 表示在每个步骤中添加噪声的方差，控制噪声强度的参数
class DDPMSampler:
    def __init__(self, generator: torch.Generator, num_training_step=1000, beta_start: float = 0.00085,
                 beta_end: float = 0.0120):
        self.betas = torch.linspace(beta_start ** 0.5, beta_end ** 0.5, num_training_step,
                                    dtype=torch.float32) ** 2  # 生成从 beta_start 到 beta_end 的 1000 个噪声强度，线性缩放调度
        # 计算一步到位加噪中的alpha
        self.alphas = 1.0 - self.betas
        self.alpha_cumprod = torch.cumprod(self.alphas, 0)  # 当前时间步前 α 值的累积乘积, alpha_0 * alpha_1 * alpha_2 ...
        self.one = torch.tensor(1.0)

        self.generator = generator  # 随机数生成器
        self.num_training_steps = num_training_step
        self.timesteps = torch.from_numpy(
            np.arange(0, num_training_step)[::-1].copy())  # 生成倒序时间步数，在推理过程中逆向去噪 [999, 998, ... 0]

    # 设置推理过程中的时间步长, 推理时间步长是从倒序时间步长中按固定间隔抽取的子集
    def set_inference_timesteps(self, num_inference_steps=50):
        self.num_inference_steps = num_inference_steps
        # 999, 998, ... 0 = 1000 steps
        # 999, 999-20, 999 - 40, ..., 0 = 50 steps  实际推理次数，20=1000/50
        step_ratio = self.num_training_steps // self.num_inference_steps  # 1000 // 50 = 20 求得推理时的时间步长
        timesteps = (np.arange(0, num_inference_steps) * step_ratio).round()[::-1].copy().astype(
            np.int64)  # 999, 999-20, 999 - 40, ..., 0
        self.timesteps = torch.from_numpy(timesteps)  # 将时间步数组转换为 PyTorch 张量并保存

    def _get_previous_timestep(self, timestep: int) -> int:
        prev_t = timestep - (self.num_training_steps // self.num_inference_steps)  # 前一时刻 = 当前时刻 - 去噪时间步长
        return prev_t

    # 向原始样本中添加噪声, original_samples 原始样本张量, timesteps 时间步长张量
    def add_noise(self, original_samples: torch.FloatTensor, timesteps: torch.IntTensor) -> torch.FloatTensor:
        alpha_cumprod = self.alpha_cumprod.to(device=original_samples.device, dtype=original_samples.dtype)
        timesteps = timesteps.to(original_samples.device)

        # 噪声均值项, 控制原始信号的比例
        sqrt_alpha_prod = alpha_cumprod[timesteps] ** 0.5
        sqrt_alpha_prod = sqrt_alpha_prod.flatten()
        while len(sqrt_alpha_prod.shape) < len(original_samples.shape):
            sqrt_alpha_prod = sqrt_alpha_prod.unsqueeze(-1)

        # 标准差项, 控制噪声的比例
        sqrt_one_minus_alpha_prod = (1 - alpha_cumprod[timesteps]) ** 0.5
        sqrt_one_minus_alpha_prod = sqrt_one_minus_alpha_prod.flatten()
        while len(sqrt_one_minus_alpha_prod.shape) < len(original_samples.shape):
            sqrt_one_minus_alpha_prod = sqrt_one_minus_alpha_prod.unsqueeze(-1)

        # 多尺度噪声：为每个样本随机选择一个噪声尺度
        scales = [0.5, 1.0, 1.5, 2.0, 2.5, 3.0]
        # scales = [0.1, 0.2, 0.5, 1.0]
        batch_size = original_samples.shape[0]
        # 生成每个样本的噪声尺度
        chosen_scales = torch.tensor(np.random.choice(scales, size=batch_size), device=original_samples.device,
                                     dtype=original_samples.dtype)
        # 调整chosen_scales形状以便广播
        while len(chosen_scales.shape) < len(original_samples.shape):
            chosen_scales = chosen_scales.unsqueeze(-1)

        # 生成多尺度噪声
        noise = torch.randn(original_samples.shape, generator=self.generator, device=original_samples.device,
                            dtype=original_samples.dtype) * chosen_scales
        noisy_samples = (sqrt_alpha_prod * original_samples) + (sqrt_one_minus_alpha_prod * noise)
        return noisy_samples, noise

    # 去噪过程
    # 计算每个时间步长对应的去噪方差
    def _get_variance(self, timestep: int) -> torch.Tensor:
        prev_t = self._get_previous_timestep(timestep)  # 获取前一时间步长

        alpha_prod_t = self.alpha_cumprod[timestep]
        alpha_prod_t_prev = self.alpha_cumprod[prev_t] if prev_t >= 0 else self.one
        current_beta_t = 1 - alpha_prod_t / alpha_prod_t_prev

        # Computed using the formula (7) of the DDPM paper
        variance = (1 - alpha_prod_t_prev) / (1 - alpha_prod_t) * current_beta_t  # 计算方差
        variance = torch.clamp(variance, min=1e-20)  # 确保方差不为零

        return variance

    # 执行扩散模型 DDPM 中的单步去噪操作: x_t -> x_t-1
    def step(self, timestep: int, latents: torch.Tensor,
             model_output: torch.Tensor):  # 当前时间步长下带噪声的隐变量，模型预测噪声 model_output
        t = timestep
        prev_t = self._get_previous_timestep(t)  # 获取前一个时间步长

        alpha_prod_t = self.alpha_cumprod[timestep]
        alpha_prod_t_prev = self.alpha_cumprod[prev_t] if prev_t >= 0 else self.one
        beta_prob_t = 1 - alpha_prod_t
        beta_prob_t_prev = 1 - alpha_prod_t_prev
        current_alpha_t = alpha_prod_t / alpha_prod_t_prev  # alpha_t
        current_beta_t = 1 - current_alpha_t

        # Compute the predicted original sample (x0) using formula (15) of the DDPM paper 计算预测的原始样本
        pred_original_sample = (
                                       latents - beta_prob_t ** 0.5 * model_output) / alpha_prod_t ** 0.5  # 根据模型预测的噪声 model_output 估计原始样本

        # Compute the coefficients for pred_original_sample (x0) and current sample x_t , 公式 (7)
        pred_original_sample_coeff = (alpha_prod_t_prev ** 0.5 * current_beta_t) / beta_prob_t  # 计算x0前的系数
        current_sample_coeff = current_alpha_t ** 0.5 * beta_prob_t_prev / beta_prob_t  # 计算xt前的系数

        # Compute the predicted previous sample mean 计算前一时间步的均值
        pred_prev_sample = pred_original_sample_coeff * pred_original_sample + current_sample_coeff * latents

        # 计算前一时间步的方差
        variance = 0  # 去噪的最后一个时刻(t=0)噪声为 0. 因为此时已经完全恢复了原始样本，不需要再添加任何噪声
        if t > 0:  # 如果当前时间步长 t > 0，则表示还需要进行去噪操作，并且需要添加相应的噪声
            device = model_output.device
            noise = torch.randn(model_output.shape, generator=self.generator, device=device,
                                dtype=model_output.dtype)  # 生成随机噪声
            variance = (self._get_variance(t) ** 0.5) * noise  # 计算标准差乘以生成的随机噪声 noise

        # N(0, 1) -> N(mu, sigma^2)
        # X = mu + sigma * Z where Z ~ N(0, 1)
        pred_prev_sample = pred_prev_sample + variance  # 计算预测的前一个时间步长的去噪样本

        return pred_prev_sample


# 训练 Diffusion 模型
diffusion = Diffusion().to(device)
diffusion_optimizer = optim.AdamW(diffusion.parameters(), lr=0.0002, weight_decay=1e-4)  # 设置L2正则化参数
diffusion_epochs = 4  # 50

# Classifier-free guidance 训练参数
condition_dropout_prob = 0.1  # 条件dropout概率，10%的样本将使用空条件

# 加载由 VAE 编码器得到的训练集隐变量和对应的工况信息
latent_for_diffusion = torch.load("latent_for_diffusion.pt").to(device)  # 加载由vae编码器得到的训练集隐变量
condition_for_diffusion = torch.load("condition_for_diffusion.pt")  # 加载对应的工况信息

# 创建训练集隐变量的 Dataloader，包含隐变量和工况信息
latent_batch_size = 32
latent_dataset = TensorDataset(latent_for_diffusion, torch.tensor(condition_for_diffusion))
latent_loader = DataLoader(latent_dataset, latent_batch_size, shuffle=True)

# 实例化工况嵌入类
condition_embedding = ConditionEmbedding(embed_dim=320).to(device)

# 初始化绘图数据
diffusion_train_losses = []

# 创建 DDPM 采样器
sampler = DDPMSampler(generator=torch.Generator(device=device), num_training_step=1000)
sampler.set_inference_timesteps(num_inference_steps=50)  # 设置推理步长

for diffusion_epoch in range(diffusion_epochs):
    print(f"Diffusion_Epoch {diffusion_epoch}")

    diffusion.train()
    diffusion_train_loss = 0.0

    for idx, (latent_batch, condition_batch) in enumerate(latent_loader, 0):
        # prepare data
        latent_batch = latent_batch.to(device)
        latent_batch = latent_batch.view(latent_batch.size(0), 1, 1, 64)  # 调整为 [32, 1, 1, 64] 或其他合适的形状

        # 提取工况信息
        rpm_batch = condition_batch[:, 0].float().to(device)  # 提取转速
        pressure_batch = condition_batch[:, 1].float().to(device)  # 提取压力

        # Classifier-free guidance: 随机mask条件
        batch_size = latent_batch.size(0)
        # 生成随机mask，True表示使用空条件
        condition_mask = torch.rand(batch_size, device=device) < condition_dropout_prob

        # 创建工况条件嵌入 (使用实际的rpm和pressure)
        context = condition_embedding(rpm_batch, pressure_batch)

        # 创建空条件嵌入（传入0, 0作为空工况）
        null_context = condition_embedding(torch.zeros_like(rpm_batch), torch.zeros_like(pressure_batch))

        # 根据mask选择使用正常条件还是空条件
        final_context = torch.where(condition_mask.unsqueeze(1), null_context, context)

        # 随机选择时间步长
        t = torch.randint(0, sampler.num_training_steps, (latent_batch.shape[0],), device=device).long()

        # 计算时间步嵌入
        t_emb = torch.stack([get_time_embedding(t_i) for t_i in t]).to(
            device)  # t 包含 Batch_size 个时间步长, 遍历 t 中的每个时间步长 t_i 进行 time embedding
        t_emb = t_emb.squeeze(1)  # (Batch_size, 320)

        # 对 latent_batch 进行加噪，得到带噪声的样本 noisy_latent 和目标噪声 noise
        noisy_latent, noise = sampler.add_noise(latent_batch, t)

        # 预测噪声（使用可能被mask的条件）
        predicted_noise = diffusion(noisy_latent, final_context, t_emb)  # 传入时间嵌入和（可能被mask的）工况嵌入

        # 计算 MSE 损失
        loss = F.mse_loss(predicted_noise, noise)

        # 反向传播和优化
        diffusion_optimizer.zero_grad()
        loss.backward()
        diffusion_optimizer.step()

        # 记录损失
        diffusion_train_loss += loss.item()

        # 每32个batch打印一次损失
        if idx % 32 == 0:
            # 统计当前批次的工况分布
            rpm_unique, rpm_counts = torch.unique(rpm_batch, return_counts=True)
            pressure_unique, pressure_counts = torch.unique(pressure_batch, return_counts=True)

            print(f"Training loss {loss:.3f} in Step {idx}")
            print(f"  Condition dropout ratio: {condition_mask.float().mean():.3f}")
            print(f"  RPM distribution: {dict(zip(rpm_unique.cpu().numpy(), rpm_counts.cpu().numpy()))}")
            print(f"  Pressure distribution: {dict(zip(pressure_unique.cpu().numpy(), pressure_counts.cpu().numpy()))}")

        # 绘制训练损失曲线
        global_iter_num_train = diffusion_epoch * len(latent_loader) + idx + 1
        Diffusion_train_wind.line([loss.item()], [global_iter_num_train], win='Diffusion_train', update='append')


# 新颖性检测结果评估
# ROC曲线 & AUC
def evaluate_roc_auc(scores, labels):
    fpr, tpr, thresholds = roc_curve(labels, scores)
    roc_auc = auc(fpr, tpr)

    plt.figure()
    lw = 2
    plt.plot(fpr, tpr, color='darkorange', lw=lw, label=f'ROC curve (area = {roc_auc:0.2f})')
    plt.plot([0, 1], [0, 1], color='navy', lw=lw, linestyle='--')
    plt.xlim([0.0, 1.0])
    plt.ylim([0.0, 1.05])
    plt.xlabel('False Positive Rate')
    plt.ylabel('True Positive Rate')
    plt.title('Receiver Operating Characteristic')
    plt.legend(loc="lower right")
    plt.show()

    # 将ROC曲线的数据保存为CSV文件
    roc_data = pd.DataFrame({'False Positive Rate': fpr, 'True Positive Rate': tpr, 'Thresholds': thresholds})
    roc_data.to_csv('roc_data.csv', index=False)

    return roc_auc


# 模型推理过程：检测阈值设置
vae.eval()
diffusion.eval()

# Classifier-free guidance 推理参数
guidance_scale = 7.5  # 引导强度，可调节参数

# 设置随机种子以确保结果可重复
seed = 42
generator = torch.Generator(device=device)
if seed is None:
    seed = torch.randint(0, 2 ** 32, (1,)).item()
generator.manual_seed(seed)

# 初始化DDPM采样器
sampler = DDPMSampler(generator=generator, num_training_step=1000)
sampler.set_inference_timesteps(num_inference_steps=10)  # 设置推理步长
timesteps = sampler.timesteps

# 按工况分组收集重构误差
condition_scores = {}  # 工况分组字典
all_scores = []  # 保留全局评分用于回退

with torch.no_grad():
    for data in train_loader:
        inputs, labels, rpm, pressure = data
        inputs = inputs.to(device)
        labels = labels.to(device)

        # VAE 编码
        mu, logvar = vae.encode(inputs)
        latents = vae.reparameterize(mu, logvar)
        latents = latents.view(latents.size(0), 1, 1, 64)

        # 创建工况条件嵌入
        context = condition_embedding(rpm, pressure)
        null_context = condition_embedding(torch.zeros_like(rpm), torch.zeros_like(pressure))

        # 对 VAE 编码得到的隐变量进行加噪
        t_start = timesteps[0]
        noisy_latent, _ = sampler.add_noise(latents, torch.full((latents.size(0),), t_start, device=device))

        # 去噪过程（加入classifier-free guidance）
        for i, timestep in enumerate(timesteps):
            time_embedding = get_time_embedding(timestep.item()).to(device)
            time_embedding = time_embedding.expand(latents.size(0), -1)

            # Classifier-free guidance
            noise_pred_cond = diffusion(noisy_latent, context, time_embedding)
            noise_pred_uncond = diffusion(noisy_latent, null_context, time_embedding)

            model_output = noise_pred_uncond + guidance_scale * (noise_pred_cond - noise_pred_uncond)
            noisy_latent = sampler.step(timestep.item(), noisy_latent, model_output)

        # 解码隐变量重构信号
        recon_inputs = vae.decode(noisy_latent)

        # 计算重构误差
        recon_loss = F.mse_loss(recon_inputs, inputs, reduction='none')
        recon_loss = recon_loss.view(recon_loss.size(0), -1).mean(dim=1)

        # 按工况分组存储重构误差
        for i, (r, p, loss) in enumerate(zip(rpm, pressure, recon_loss)):
            condition_key = get_condition_key(r.item(), p.item())  # 使用统一的condition_key格式函数
            if condition_key not in condition_scores:
                condition_scores[condition_key] = []
            condition_scores[condition_key].append(loss.item())  # 将当前样本重构误差添加到对应工况列表

        # 同时收集全局评分（用于回退）
        all_scores.extend(recon_loss.cpu().numpy())

    # 为每个工况计算专用阈值
    condition_thresholds = {}  # 存储各工况的阈值
    condition_stats = {}  # 存储各工况的统计信息

    # 置信度选项 - 可根据应用需求调整
    confidence_options = {
        'conservative': 0.999,  # 保守：极低误报率(0.1%)
        'balanced': 0.995,  # 平衡：低误报率(0.5%) - 推荐
        'sensitive': 0.99,  # 敏感：中等误报率(1%)
        'screening': 0.95  # 筛查：较高误报率(5%)
    }

    confidence = confidence_options['balanced']  # 当前使用平衡模式
    print(f"Using confidence level: {confidence} ({confidence * 100:.1f}% coverage)")

    print("====== Multi-Condition Threshold Calculation ======")

    for condition_key, scores in condition_scores.items():
        scores_array = np.array(scores)
        mu, std = norm.fit(scores_array)
        threshold = norm.ppf(confidence, loc=mu, scale=std)  # 计算阈值

        condition_thresholds[condition_key] = float(threshold)  # 存储结果
        condition_stats[condition_key] = {
            "mu": float(mu),
            "std": float(std),
            "count": len(scores),
            "threshold": float(threshold)
        }

        print(f"Condition {condition_key}: μ={mu:.6f}, σ={std:.6f}, threshold={threshold:.6f}, samples={len(scores)}")

    # 计算全局阈值作为回退选项
    all_scores_array = np.array(all_scores)
    global_mu, global_std = norm.fit(all_scores_array)
    global_threshold = norm.ppf(confidence, loc=global_mu, scale=global_std)

    print(f"\nGlobal fallback: μ={global_mu:.6f}, σ={global_std:.6f}, threshold={global_threshold:.6f}")
    print(f"Guidance scale used: {guidance_scale}")
    print("=" * 50)

    # 保存多工况阈值信息
    threshold_data = {
        "condition_thresholds": condition_thresholds,
        "condition_stats": condition_stats,
        "global_stats": {
            "mu": float(global_mu),
            "std": float(global_std),
            "threshold": float(global_threshold),
            "count": len(all_scores_array)
        },
        "confidence": float(confidence),
        "guidance_scale": float(guidance_scale)
    }

    with open("multi_condition_thresholds.json", "w") as f:
        json.dump(threshold_data, f, indent=2)

    print("Multi-condition thresholds saved to 'multi_condition_thresholds.json'")


# 实现工况感知的阈值查询机制
def get_condition_threshold(rpm, pressure, condition_thresholds, global_threshold=None):
    """
    根据工况获取对应阈值，处理未知工况的策略
    """
    condition_key = get_condition_key(rpm, pressure)  # 使用统一的condition_key格式函数

    # 策略1：直接匹配
    if condition_key in condition_thresholds:
        return condition_thresholds[condition_key]

    # 策略2：未知工况处理 - 最近邻策略：返回最近工况的阈值代替
    if len(condition_thresholds) > 0:
        min_distance = float('inf')
        nearest_threshold = None

        for cond_str, threshold in condition_thresholds.items():
            # 解析已知工况
            cond_str_clean = cond_str.strip("()")
            known_rpm, known_pressure = map(float, cond_str_clean.split(", "))

            # 计算欧氏距离
            distance = ((rpm - known_rpm) ** 2 + (pressure - known_pressure) ** 2) ** 0.5
            if distance < min_distance:
                min_distance = distance
                nearest_threshold = threshold

        print(f"Unknown condition ({rpm}, {pressure}), using nearest neighbor threshold: {nearest_threshold:.6f}")
        return nearest_threshold

    # 策略3：回退到全局阈值
    if global_threshold is not None:
        print(f"Using global fallback threshold: {global_threshold:.6f}")
        return global_threshold

    # 最后的保险策略
    return 0.5  # 默认阈值


# 加载之前计算并保存的多工况阈值信息
try:
    with open("multi_condition_thresholds.json", "r") as f:
        threshold_data = json.load(f)

    condition_thresholds = threshold_data["condition_thresholds"]
    condition_stats = threshold_data["condition_stats"]
    global_threshold = threshold_data["global_stats"]["threshold"]
    guidance_scale = threshold_data.get("guidance_scale", 7.5)
    confidence = threshold_data.get("confidence", 0.995)

    print("====== Loaded Multi-Condition Thresholds ======")
    for condition, threshold in condition_thresholds.items():
        stats = condition_stats[condition]
        print(f"Condition {condition}: threshold={threshold:.6f}, samples={stats['count']}")
    print(f"Global fallback threshold: {global_threshold:.6f}")
    print("=" * 45)

except FileNotFoundError:
    print("Multi-condition threshold file not found. Please run threshold calculation first.")
    # 设置默认值以防文件不存在
    condition_thresholds = {}
    global_threshold = 0.5
    guidance_scale = 7.5

# 模型推理过程：工况感知的新颖性检测
vae.eval()
diffusion.eval()

# 设置随机种子以确保结果可重复
seed = 42
generator = torch.Generator(device=device)
if seed is None:
    seed = torch.randint(0, 2 ** 32, (1,)).item()
generator.manual_seed(seed)

# 初始化DDPM采样器
sampler = DDPMSampler(generator=generator, num_training_step=1000)
sampler.set_inference_timesteps(num_inference_steps=10)  # 设置推理步长
timesteps = sampler.timesteps

# 收集测试结果
test_scores = []
test_labels = []
test_conditions = []
condition_wise_scores = {}  # 按工况分组的测试结果

with torch.no_grad():
    for data in val_loader:
        inputs, labels, rpm, pressure = data
        inputs = inputs.to(device)
        labels = labels.to(device)

        # VAE 编码
        mu, logvar = vae.encode(inputs)
        latents = vae.reparameterize(mu, logvar)
        latents = latents.view(latents.size(0), 1, 1, 64)

        # 创建工况条件嵌入
        context = condition_embedding(rpm, pressure)
        null_context = condition_embedding(torch.zeros_like(rpm), torch.zeros_like(pressure))

        # 对 VAE 编码得到的隐变量进行加噪
        t_start = timesteps[0]
        noisy_latent, _ = sampler.add_noise(latents, torch.full((latents.size(0),), t_start, device=device))

        # 去噪过程（加入classifier-free guidance）
        for i, timestep in enumerate(timesteps):
            time_embedding = get_time_embedding(timestep.item()).to(device)
            time_embedding = time_embedding.expand(latents.size(0), -1)

            # Classifier-free guidance: 同时计算有条件和无条件的噪声预测
            noise_pred_cond = diffusion(noisy_latent, context, time_embedding)
            noise_pred_uncond = diffusion(noisy_latent, null_context, time_embedding)

            # 组合预测结果
            model_output = noise_pred_uncond + guidance_scale * (noise_pred_cond - noise_pred_uncond)

            # 执行一步去噪
            noisy_latent = sampler.step(timestep.item(), noisy_latent, model_output)

        # 解码隐变量重构信号
        recon_inputs = vae.decode(noisy_latent)

        # 计算重构误差
        recon_loss = F.mse_loss(recon_inputs, inputs, reduction='none')
        recon_loss = recon_loss.view(recon_loss.size(0), -1).mean(dim=1)

        # 收集测试数据，将每个样本的信息按工况分组存储
        for i, (r, p, score, label) in enumerate(zip(rpm, pressure, recon_loss, labels)):
            condition_key = get_condition_key(r.item(), p.item())  # 使用统一的condition_key格式函数

            test_scores.append(score.item())
            test_labels.append(label.item())
            test_conditions.append(condition_key)

            # 按工况分组存储
            if condition_key not in condition_wise_scores:
                condition_wise_scores[condition_key] = {"scores": [], "labels": []}
            condition_wise_scores[condition_key]["scores"].append(score.item())
            condition_wise_scores[condition_key]["labels"].append(label.item())

# 使用工况特定阈值进行预测
print("====== Multi-Condition Anomaly Detection Results ======")

# 全局性能评估
all_predictions_global = []  # 存储使用全局阈值判断是否为异常的结果
all_predictions_condition = []  # 存储使用当前工况对应阈值判断是否为异常的结果

for i, (score, condition, label) in enumerate(zip(test_scores, test_conditions, test_labels)):
    # 解析工况信息
    condition_clean = condition.strip("()")
    rpm_val, pressure_val = map(float, condition_clean.split(", "))

    # 获取工况特定阈值
    condition_threshold = get_condition_threshold(rpm_val, pressure_val, condition_thresholds, global_threshold)

    # 进行预测
    pred_global = score > global_threshold  # 判断当前样本的重构误差是否超过了全局阈值
    pred_condition = score > condition_threshold  # 判断是否超过当前工况特定的阈值

    all_predictions_global.append(int(pred_global))  # 储存预测结果（全局阈值）
    all_predictions_condition.append(int(pred_condition))  # 储存预测结果（工况特定阈值）

# 计算并比较性能
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix

# 全局阈值性能
global_auc = evaluate_roc_auc(test_scores, test_labels)
global_accuracy = accuracy_score(test_labels, all_predictions_global)
global_precision = precision_score(test_labels, all_predictions_global)
global_recall = recall_score(test_labels, all_predictions_global)
global_f1 = f1_score(test_labels, all_predictions_global)

# 计算全局FPR
cm_global = confusion_matrix(test_labels, all_predictions_global)
tn_global, fp_global, fn_global, tp_global = cm_global.ravel()
global_fpr = fp_global / (fp_global + tn_global) if (fp_global + tn_global) > 0 else 0.0

# 工况特定阈值性能
condition_auc = evaluate_roc_auc(test_scores, test_labels)  # AUC基于原始分数，不受阈值影响
condition_accuracy = accuracy_score(test_labels, all_predictions_condition)
condition_precision = precision_score(test_labels, all_predictions_condition)
condition_recall = recall_score(test_labels, all_predictions_condition)
condition_f1 = f1_score(test_labels, all_predictions_condition)

# 计算工况特定FPR
cm_condition = confusion_matrix(test_labels, all_predictions_condition)
tn_condition, fp_condition, fn_condition, tp_condition = cm_condition.ravel()
condition_fpr = fp_condition / (fp_condition + tn_condition) if (fp_condition + tn_condition) > 0 else 0.0

# 打印性能对比
print("\n📊 Performance Comparison:")
print("=" * 70)
print(f"{'Metric':<20} {'Global Threshold':<20} {'Condition-Specific':<20}")
print("=" * 70)
print(f"{'AUC':<20} {global_auc:<20.4f} {condition_auc:<20.4f}")
print(f"{'Accuracy':<20} {global_accuracy:<20.4f} {condition_accuracy:<20.4f}")
print(f"{'Precision':<20} {global_precision:<20.4f} {condition_precision:<20.4f}")
print(f"{'Recall':<20} {global_recall:<20.4f} {condition_recall:<20.4f}")
print(f"{'F1-Score':<20} {global_f1:<20.4f} {condition_f1:<20.4f}")
print(f"{'FPR':<20} {global_fpr:<20.4f} {condition_fpr:<20.4f}")
print("=" * 70)

# 按工况分析性能
print("\n📋 Condition-wise Performance Analysis:")
print("=" * 120)
for condition_key in condition_wise_scores:
    cond_data = condition_wise_scores[condition_key]
    if len(cond_data["scores"]) > 0:
        # 获取该工况的阈值
        condition_clean = condition_key.strip("()")
        rpm_val, pressure_val = map(float, condition_clean.split(", "))
        threshold = get_condition_threshold(rpm_val, pressure_val, condition_thresholds, global_threshold)

        # 计算该工况的性能
        cond_scores = np.array(cond_data["scores"])
        cond_labels = np.array(cond_data["labels"])
        cond_preds = (cond_scores > threshold).astype(int)

        if len(np.unique(cond_labels)) > 1:  # 确保有正负样本
            cond_auc = evaluate_roc_auc(cond_scores, cond_labels)
            cond_acc = accuracy_score(cond_labels, cond_preds)
            cond_prec = precision_score(cond_labels, cond_preds)
            cond_recall = recall_score(cond_labels, cond_preds)
            cond_f1 = f1_score(cond_labels, cond_preds)

            # 计算该工况的FPR
            cm_cond = confusion_matrix(cond_labels, cond_preds)
            tn_cond, fp_cond, fn_cond, tp_cond = cm_cond.ravel()
            cond_fpr = fp_cond / (fp_cond + tn_cond) if (fp_cond + tn_cond) > 0 else 0.0

            print(f"Condition {condition_key}:")
            print(f"  AUC={cond_auc:.4f}, Accuracy={cond_acc:.4f}, Precision={cond_prec:.4f}")
            print(f"  Recall={cond_recall:.4f}, F1={cond_f1:.4f}, FPR={cond_fpr:.4f}")
            print(f"  Threshold={threshold:.6f}, Samples={len(cond_scores)}")
        else:
            print(f"Condition {condition_key}: Single class only, Samples={len(cond_scores)}")

print("=" * 120)

# 混淆矩阵对比
from sklearn.metrics import confusion_matrix, classification_report

print("\n🔍 Detailed Analysis:")
print("\n1. Global Threshold Results:")
print("Confusion Matrix:")
print(cm_global)
print("Classification Report:")
print(classification_report(test_labels, all_predictions_global))

print("\n2. Condition-Specific Threshold Results:")
print("Confusion Matrix:")
print(cm_condition)
print("Classification Report:")
print(classification_report(test_labels, all_predictions_condition))

# 绘制重构误差分布柱状图（分工况）
scores_array = np.array(test_scores)
true_labels_array = np.array(test_labels)
normal_scores = scores_array[true_labels_array == 0]
anomaly_scores = scores_array[true_labels_array == 1]

plt.figure(figsize=(12, 8))
plt.hist(normal_scores, bins=20, alpha=0.7, color='blue', edgecolor='black', label='Normal Data')
plt.hist(anomaly_scores, bins=20, alpha=0.7, color='red', edgecolor='black', label='Anomaly Data')

# 添加阈值分割线 - 全局阈值
plt.axvline(global_threshold, color='black', linestyle='dashed', linewidth=2,
            label=f'Global Threshold = {global_threshold:.4f}')

# 添加工况特定阈值的范围
threshold_values = list(condition_thresholds.values()) if condition_thresholds else [global_threshold]
if threshold_values:
    min_threshold = min(threshold_values)
    max_threshold = max(threshold_values)
    plt.axvspan(min_threshold, max_threshold, alpha=0.2, color='green',
                label=f'Condition Thresholds Range [{min_threshold:.4f}, {max_threshold:.4f}]')

plt.title('Multi-Condition Reconstruction Error Distribution')
plt.xlabel('Novelty Score')
plt.ylabel('Frequency')
plt.legend()
plt.show()

print(f"\n✨ Multi-Condition Threshold System Implementation Complete!")
print(f"📈 Performance Improvement: {condition_f1 - global_f1:+.4f} F1-Score")
print(f"🎯 Condition-specific thresholds: {len(condition_thresholds)} conditions")
print(f"🔧 Used guidance scale: {guidance_scale:.4f}")

